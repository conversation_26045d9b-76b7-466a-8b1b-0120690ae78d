from .agent_mysql import Mysql
import importlib, inspect
from .agent_business import Business




class Agent:
    def __init__(self):
        self.mysql = Mysql()
        self.ID = 0
        self.NOME   = None
        self.HISTORICO = 0
        self.ARQUIVO = None
        self.instancia = None
        

    async def fetch_agent_nick(self,columns:str,nick:str):
        #agente = await self.mysql.query (f"SELECT {columns} FROM AGENTE WHERE NICK1 = '{nick}' OR NICK2 = '{nick}'")
        agente = await self.mysql.query(f"""
            SELECT {columns}
            FROM VISAO_AGENTE_FUNCAO 
            WHERE NICK1 = '{nick}' OR NICK2 = '{nick}'
            """)

        if agente:
           agente = agente[0]
           #print("agente nick",agente)
           #exit()
           for column in agente.keys():
                setattr(self, column, agente[column])
           return agente 
        else:
           return None
        


    async def fetch_agent_id(self,columns:str,id:int):
        agente = await self.mysql.query(f"""
            SELECT {columns}
            FROM VISAO_AGENTE_FUNCAO
            WHERE ID = {id}
            """)

        if agente:
           agente = agente[0]
           for column in agente.keys():
                setattr(self, column, agente[column])
           return agente 
        else:
           return []


    async def fetch_channel(self,columns:str,id:str):
        #print("========== agente.fetch_channel() ==========",id)
        agente = await self.mysql.query (f"SELECT {columns} FROM VISAO_AGENTE_FUNCAO WHERE CANAL_ID ='{id}'")
        #print("agente",agente)
        if agente:
           agente = agente[0]
           for column in agente.keys():
                setattr(self, column, agente[column])
           return agente 
        else:
           return []


    async def get_instance(self,usuario=None):
        if self.instancia:
            return self.instancia
        
        if not self.ID:
            print("Agente não carregado")
            return None
        
        if not self.ARQUIVO:
            print("Arquivo não informado")
            return
        
        arquivo = self.ARQUIVO if self.ID !=1  else "agent_gptalk"
        module = importlib.import_module(f".{arquivo}", package="api.agent")
        #print("module",module)
    
   
        classes = [member for name, member in inspect.getmembers(module,  inspect.isclass) if member.__module__ == module.__name__]
        #print ("classes",classes)
    
        if len(classes) != 1:
            raise Exception("O módulo deve conter exatamente uma classe.")
            return
        
        agent_class = classes[0]
        #print("agent_class", agent_class)


        # Identificar os parâmetros necessários para a instância da classe
        init_params = inspect.signature(agent_class.__init__).parameters
        init_args = {}


        if 'usuario' in init_params: #se o agente requer um usuário
           init_args['usuario'] = usuario
           #print ("usuario",usuario.NOME)

        if 'negocio' in init_params: #se o agente requer um negócio
            negocio = Business()
            await negocio.fetch("*",[f"ID={self.NEGOCIO_ID}"])
            init_args['negocio'] = negocio
            #print ("negocio",negocio.NOME)

        agent_instance = agent_class(**init_args)
        #print("agent_instance", agent_instance)
        self.instancia = agent_instance
        return agent_instance

    async def listar_agentes_monitor(self):
        """
        Lista todos os agentes disponíveis para o filtro do monitor de mensagens
        Retorna agentes únicos da tabela AGENTE que não estão excluídos
        """
        try:
            query = "SELECT ID, NOME FROM AGENTE WHERE EXCLUIDO = 0 ORDER BY NOME"
            result = await self.mysql.query(query)
            return result
        except Exception as e:
            print(f"Erro ao buscar agentes para monitor: {e}")
            return []


if __name__ == "__main__":
    import asyncio


    async def main():
        from .agent_user import User
        agente = Agent()
        usuario = User()
        await usuario.fetch("ID,NOME",[f"ID=1"])

        
        await agente.fetch_agent_id("*",10)
        
        #print("agente",agente.__dict__)
        agent_instance = await agente.get_instance(usuario=usuario)

    asyncio.run(main())