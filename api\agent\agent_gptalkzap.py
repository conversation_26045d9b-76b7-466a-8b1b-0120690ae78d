# ==========================================
from fastapi import APIRouter, Request, HTTPException
from .agent_logger import AgentLogger
from ..cache import cache
from .agent_user import User
from .agent_llm import LLM
from ..functions.util import is_local
from .agent_openai import OpenAi
from agents import function_tool
import httpx
from .agent_mysql import AgentMysql  # Adicionado para integrar o agente MySQL
import json

logger = AgentLogger()
router = APIRouter()
oai = OpenAi()
user = User()
agente_roteador_idx = "gptalkzap"
model = "1234567895"
# Definir as ferramentas fora da classe


@function_tool
async def lista_agentes():
    """
    Lista os agentes disponíveis
    """
    return """
@gizy - Este sou eu.
@maria - Atendente e suporte do app Assistencia MK, que dá suporte as consultoras da <PERSON>.
@tech - Secretaria para oficinas mecânicas ,seja de carros , motos bicicletas ou outros veículos. O app gerenciado por ela é o Oficina Tech.
@mysql - Agente especialista em consultas e operações com banco de dados MySQL.
"""


class AgentGptalkZap:  # PascalCase

    def __init__(self):
        self.instructions = ""
        # Removido self.historico_mensagens para evitar estado compartilhado
        self.intencao = ""

    async def validate_agent(self, nick):
        nick = nick.lower()
        match nick:
            case "@gptalkzap" | "@gptalk":
                return True
            #case "@mariakarla":
                #return True
            #case "@mariakenia":
                #return True
            #case "@maria":
                #return True
            #case "@tech":
                #return True
            case "@oficinatech" | "@tech":
                return True
            #case "@sofia" | "@brain":
                #return True
            #case "@neo" | "@neo4j":
                #return True
            #case "@servervps" | "@server" | "@vps":
                #return True
            #case "@mysql":
                #return True
            case _:
                return

    async def welcome_message(self, nick):
        """
        Retorna mensagem de boas-vindas baseada no agente selecionado
        """
        nick = nick.lower()
        match nick:
            case "@gptalkzap" | "@gptalk":
                return {
                    "status": "success",
                    "message": "" + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/gptalk/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@mariakarla" | "@mariakenia" | "@maria" | "@assistenciamk":
                return {
                    "status": "success",
                    "message": "Em que posso ajuda-lo?",
                    "media_url": "https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@tech" | "@oficinatech":
                return {
                    "status": "success",
                    "message": "Em que posso ajuda-lo?",
                    "media_url": "https://gptalk.com.br/app_phj/app/oficinatech/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@neo" | "@neo4j":
                return {
                    "status": "success",
                    "message": "",
                    "media_url": "https://gptalk.com.br/app_phj/app/neo/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@brain" | "@sofia":
                return {
                    "status": "success",
                    "message": "O que deseja aprender , memorizar ou lembrar?",
                    "media_url": "https://gptalk.com.br/app_phj/app/brain/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@servervps" | "@server" | "@vps":
                return {
                    "status": "success",
                    "message": "Olá, boa noite! Eu sou o agente " + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/servervps/imagens/cardzap.png",
                    "media_type": "image"
                }
            case "@mysql":
                return {
                    "status": "success",
                    "message": "Olá, boa noite! Eu sou o agente " + nick,
                    "media_url": "https://gptalk.com.br/app_phj/app/mysql/imagens/cardzap.png",
                    "media_type": "image"
                }
            case _:
                return {"status": "success", "message": "Olá, boa noite! Eu sou o agente " + nick}

    def get_instructions(self, usuario_nome: str, usuario_telefone: str):
        self.instructions = f"""
        Você é um agente de atendimento ao cliente da empresa IAPN. Seu nome é Gizy.
        Sua função é esclarecer dúvidas dos usuários sobre o serviço GPTtalk Zap, que dá acesso aos serviços e IAs do sistema GPTalk. 
        No momento você está atendendo o usuário {usuario_nome} com o telefone {usuario_telefone}.
        Você é educado, divertido , prestativo e responde de forma clara, alegre, mas objetiva.
        """
        return self.instructions

    async def agent_create(self, modelo: str, usuario_nome: str, usuario_telefone: str):

        llm = LLM()
        model = llm.get_model_idx(modelo)

        instructions = self.get_instructions(usuario_nome, usuario_telefone)

        # Criar lista base de tools (comum para todos os usuários)
        tools_list = [
            lista_agentes,
        ]

        agente = {
            "name": "GPalkZap",
            "instructions": instructions,
            "model": model,
            "tools": tools_list,
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,  # Temporariamente desabilitado devido ao errocom     json_schema
            "input_guardrails": [],
            "output_guardrails": [],
        }

        # Criar o agente
        agente_obj = await oai.agent_create(**agente)

        return agente_obj

    async def detectar_intencao_usuario(self, mensagem: str):
        """
        Detecta a intenção do usuário com base na mensagem
        """
        pass

    def add_message_to_history(self, history: list, message: str, is_user: bool = True) -> list:
        """Adiciona uma mensagem ao histórico no formato padrão de chat"""
        # print("===== add_message_to_history() =====")
        # print("history", history)
        # print("message", message)
        # print("is_user", is_user)
        if not isinstance(history, list):
            history = []

        message_dict = {
            "role": "user" if is_user else "assistant",
            "content": str(message).strip()
        }

        history.append(message_dict)
        return history

    async def get_create_user_talk_cache_key(self, telefone: str, agente_atual: str):

        # Verifica se ja existe mensagens no cache deste numero de telefone e agente específico
        # Buscar chave específica para este agente e telefone
        cache_conversa_key = None
        usuario_idx = None

        # Procurar por chave específica para este agente e telefone
        for key in cache.keys():
            if telefone in str(key) and agente_atual in str(key):
                cache_conversa_key = key
                # Extrair usuario_idx da chave no formato: conversa_{agente}_{usuario_idx}_{telefone}
                parts = cache_conversa_key.split("_")
                if len(parts) >= 4:
                    usuario_idx = parts[2]
                break

        # Se não encontrou cache específico, buscar usuário e criar nova chave
        if not cache_conversa_key:
            usuario = await user.search_phone(telefone)

            if not usuario:
                usuario_idx = "0"
            else:
                usuario_idx = usuario["idx"]
            cache_conversa_key = "conversa_" + agente_atual + \
                "_" + usuario_idx + "_" + telefone

        return usuario_idx, cache_conversa_key

    async def send_to_agent(
        self,
        mensagem: str,
        agente_atual: str,
        usuario_idx: str,
        usuario_nome: str,
        imagem: str,
        plataforma_url: str,
        api_url_base: str
    ):
        """
        Envia a mensagem para outros agentes
        """
        try:
            # Implementando o "switch" igual ao da função validate_agent()
            match agente_atual:
                case "@mariakarla" | "@mariakenia" | "@maria":
                    # Montando um dicionário de dados para envio ao agente Maria Kênia
                    data = {
                        "usuario_funcao": 1,
                        "modelo": "",  # Defina o valor de modelo conforme necessário
                        "imagem": imagem,
                        "usuario_idx": usuario_idx,
                        "plataforma_url": plataforma_url,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto_zap",
                        "mensagem": mensagem,
                        "chamador": "@gptalkzap"  # ✅ Identificar que é chamada de agente
                    }

                    url = api_url_base + "/agent/assistenciamk/send/text"

                    # Fazendo a requisição HTTP
                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.post(url, json=data)
                        response.raise_for_status()  # Levanta exceção se status HTTP for de erro

                        # Verificar o tipo de resposta pelo Content-Type
                        content_type = response.headers.get("content-type", "")

                        if "application/json" in content_type:
                            # Resposta JSON (modos: audio, voz_ao_vivo, texto_voz_ao_vivo)
                            response_data = response.json()
                            return response_data
                        elif "text/plain" in content_type:
                            # Resposta de texto plano (modo texto normal - streaming)
                            response_text = response.text
                            return {"status": "success", "message": response_text}
                        else:
                            # Tipo de resposta desconhecido
                            response_text = response.text
                            logger.warning(
                                f"Tipo de resposta desconhecido ({content_type}) de {agente_atual}: {response_text}")
                            return {"status": "success", "message": response_text}

                case "@oficinatech" | "@tech":
                    negocio_idx = "4015743441"
                    data = {
                        "usuario_funcao": 1,
                        "modelo": "",  # Defina o valor de modelo conforme necessário
                        "imagem": imagem,
                        "usuario_idx": usuario_idx,
                        "negocio_idx": negocio_idx,
                        "plataforma_url": plataforma_url,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto_zap",
                        "mensagem": mensagem,
                        "agente": "@gptalkzap"  # ✅ Identificar que é chamada de agente
                    }

                    url = api_url_base + "/agent/oficinatech/send/text"

                case "@brain" | "@sofia":
                    data = {
                        "usuario_funcao": 1,
                        "modelo": "",  # Defina o valor de modelo conforme necessário
                        "imagem": imagem,
                        "usuario_idx": usuario_idx,
                        "plataforma_url": plataforma_url,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto",
                        "mensagem": mensagem,
                        "agente": "@gptalkzap"
                    }

                    url = api_url_base + "/agent/brain/send/text"

                case "@neo" | "@neo4j":
                    data = {
                        "usuario_funcao": 1,
                        "modelo": "",  # Defina o valor de modelo conforme necessário
                        "imagem": imagem,
                        "usuario_idx": usuario_idx,
                        "negocio_idx": usuario_idx,  # Neo4j usa negocio_idx ao invés de usuario_idx
                        "plataforma_url": plataforma_url,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto_zap",
                        "mensagem": mensagem,
                        "agente": "@gptalkzap"
                    }

                    url = api_url_base + "/agent/neo4j/send/text"
                case "@vps" | "@servervps":
                    data = {
                        "mensagem": mensagem,
                        "modelo": "1234567895",  # Defina o valor de modelo conforme necessário
                        "usuario_idx": usuario_idx,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto_zap",

                    }
                    url = api_url_base + "/agent/servervps/send/text"
                case "@mysql":
                    data = {
                        "mensagem": mensagem,
                        "modelo": "gpt-4o",  # ou outro modelo adequado
                        "usuario_idx": usuario_idx,
                        "usuario_nome": usuario_nome,
                        "modo_resposta_ia": "texto_zap",
                    }
                    url = api_url_base + "/agent/mysql/send/text"

                case _:
                    raise ValueError(f"Agente desconhecido: {agente_atual}")

            # Fazendo a requisição HTTP
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, json=data)
                response.raise_for_status()  # Levanta exceção se status HTTP for de erro

                # Verificar o tipo de resposta pelo Content-Type
                content_type = response.headers.get("content-type", "")

                if "application/json" in content_type:
                    # Resposta JSON (modos: audio, voz_ao_vivo, texto_voz_ao_vivo)
                    response_data = response.json()
                    return response_data
                elif "text/plain" in content_type:
                    # Resposta de texto plano (modo texto normal - streaming)
                    # Para agentes que retornam streaming, precisamos ler todo o conteúdo
                    if agente_atual in ["@neo", "@neo4j"]:
                        # Para Neo4j, ler todo o stream de uma vez
                        response_text = ""
                        async for chunk in response.aiter_text():
                            response_text += chunk
                        return {"status": "success", "message": response_text}
                    else:
                        # Para outros agentes, usar response.text normal
                        response_text = response.text
                        return {"status": "success", "message": response_text}
                else:
                    # Tipo de resposta desconhecido
                    response_text = response.text
                    logger.warning(
                        f"Tipo de resposta desconhecido ({content_type}) de {agente_atual}: {response_text}")
                    return {"status": "success", "message": response_text}

        except httpx.HTTPStatusError as e:
            logger.error(
                f"Erro HTTP ao enviar para {agente_atual}: {e.response.status_code} - {e.response.text}")
            return {"status": "error", "message": f"Erro HTTP: {e.response.status_code}"}

        except httpx.RequestError as e:
            logger.error(
                f"Erro de conexão ao enviar para {agente_atual}: {str(e)}")
            return {"status": "error", "message": "Erro de conexão com o agente"}

        except Exception as e:
            logger.error(
                f"Erro inesperado ao enviar para {agente_atual}: {str(e)}")
            return {"status": "error", "message": f"Erro inesperado: {str(e)}"}

    async def send(self, data: dict):
        """
        Método send para ser chamado por outros agentes
        Agora retorna um array de mensagens ao invés de uma única mensagem
        """
        try:

            # Array de mensagens a serem retornadas
            messages_array = []

            # 🎯 OBTER DADOS DO USUÁRIO PRIMEIRO para criar cache específico
            message = data["message"]
            sender_name = data["sender_name"]
            sender_phone = data["sender_phone"]
            

            # Validações de entrada
            if not message:
                return {"status": "fail", "messages": [], "error": "Mensagem é obrigatória"}


            if not sender_phone:
                return {"status": "fail", "messages": [], "error": "Telefone é obrigatório"}


            # 🎯 CACHE ESPECÍFICO POR USUÁRIO: usar telefone como identificador único
            cache_key_agente = f"agente_atual_{sender_phone}"
            agente_atual = cache.get(cache_key_agente, None)

            # 🔄 RENOVAÇÃO AUTOMÁTICA: A cada mensagem, renovar TTL do agente por mais 15 minutos
            if agente_atual:
                # Renova por mais 15 minutos (TTL global)
                cache[cache_key_agente] = agente_atual

            # Verifica se a mensagem é para a ativação de um agente
            # se mensagem começar com @, verifica se o nick é valido e atualiza o cache
            if message.startswith("@"):
                # Divide apenas na primeira ocorrência do espaço
                partes = message.split(" ", 1)
                nick = partes[0]  # Primeira parte é o nome do agente
                texto_adicional = partes[1].strip() if len(
                    partes) > 1 else ""  # Segunda parte é o texto adicional

                agente_existe = await self.validate_agent(nick)
                if agente_existe:
                    # 15 minutos com TTL global - específico por usuário
                    cache[cache_key_agente] = nick
                    welcome_msg = await self.welcome_message(nick)
                    # Adicionar mensagem de boas-vindas ao array
                    messages_array.append(welcome_msg)

                    # Se não há texto adicional, retorna apenas a mensagem de boas-vindas
                    if not texto_adicional:
                        return {"status": "success", "messages": messages_array}

                    # Se há texto adicional, atualiza a mensagem e continua o fluxo
                    message = texto_adicional
                    agente_atual = nick  # Atualiza o agente_atual para o fluxo continuar
                else:
                    error_msg = {
                        "status": "fail",
                        "message": "Não foi possível identificar a IA. Por favor, verifique se o nome está correto. Você pode informar também o nome do app. Exemplo: @oficinatech",
                        "media_url": None,
                        "media_type": None
                    }
                    messages_array.append(error_msg)
                    return {"status": "fail", "messages": messages_array}

            # Se não houver nenhum agente atual, setar como '@gptalkzap' e adicionar mensagem de boas-vindas
            if not agente_atual:
                agente_atual = "@gptalkzap"
                # 15 minutos com TTL global - específico por usuário
                cache[cache_key_agente] = agente_atual

                # Adicionar mensagem de boas-vindas ao array
                welcome_msg = await self.welcome_message(agente_atual)
                messages_array.append(welcome_msg)

            plataforma_url = "https://www.gptalk.com.br"
            api_url_base = "https://server.gptalk.com.br/api"

            # Definindo a URL completa com protocolo
            if is_local():
                plataforma_url = "http://127.0.0.1:8000/gptalk"
                api_url_base = "http://127.0.0.1:8000/api"

            # Se o agente atual não for o gptalkzap, redirecionar para outro agente
            if agente_atual != "@gptalkzap":
                # Primeiro precisamos obter o usuario_idx
                usuario_idx, _ = await self.get_create_user_talk_cache_key(sender_phone, agente_atual)

                # Obter imagem dos dados se disponível
                imagem = data.get("image", "")

                response = await self.send_to_agent(
                    mensagem=message,
                    agente_atual=agente_atual,
                    usuario_idx=usuario_idx,
                    usuario_nome=sender_name,
                    imagem=imagem,
                    plataforma_url=plataforma_url,
                    api_url_base=api_url_base
                )

                # Adicionar resposta do outro agente ao array
                messages_array.append(response)
                
                return {"status": "success", "messages": messages_array}

            # Usar a função comum para processar com o agente
            agente_obj = await self.agent_create(
                modelo=model,
                usuario_nome=sender_name,
                usuario_telefone=sender_phone,
            )


            # Detectar intenção do usuario
            intencao = await self.detectar_intencao_usuario(message)

            usuario_idx, conversa_idx = await self.get_create_user_talk_cache_key(sender_phone, agente_atual)


            # Inicializar historico_mensagens como lista vazia
            historico_mensagens = []
            if conversa_idx in cache:
                historico_mensagens = cache[conversa_idx]

            historico_mensagens = self.add_message_to_history(
                historico_mensagens, message, True)

            # Usar agent_run_sync para obter resposta completa diretamente
            response = await oai.agent_run_sync(agente_obj, historico_mensagens)

            # Extrair o texto da resposta
            resposta_texto = ""
            if hasattr(response, 'final_output') and response.final_output:
                resposta_texto = str(response.final_output)

            # Adicionar a resposta do agente ao histórico
            if resposta_texto:
                historico_mensagens = self.add_message_to_history(
                    historico_mensagens, resposta_texto, False)
                # Salvar histórico atualizado no cache
                cache[conversa_idx] = historico_mensagens

                # Adicionar resposta processada ao array
                agent_response = {
                    "status": "success",
                    "message": resposta_texto,
                    "media_url": None,
                    "media_type": None
                }
                messages_array.append(agent_response)

            result = {"status": "success", "messages": messages_array}

            return result

        except Exception as e:
            logger.error(f"Erro no método send do AgentGptalkZap: {str(e)}")
            raise

    def renomear_cache_conversa(self, old_key: str, new_key: str) -> bool:
        """
        Renomeia uma chave de conversa no cache

        Args:
            old_key (str): Chave antiga (ex: conversa_gptalkzap_0_553184198720)
            new_key (str): Nova chave (ex: conversa_gptalkzap_1234567890_553184198720)

        Returns:
            bool: True se a renomeação foi bem-sucedida, False caso contrário
        """
        try:
            if old_key in cache:
                # Move o histórico para a nova chave
                cache[new_key] = cache.pop(old_key)
                return True
            else:
                logger.warning(f"Chave {old_key} não encontrada no cache")
                return False
        except Exception as e:
            logger.error(f"Erro ao renomear cache: {str(e)}")
            return False

    def listar_chaves_cache(self, filtro: str = None) -> list:
        """
        Lista todas as chaves do cache, opcionalmente com filtro

        Args:
            filtro (str): Filtro opcional para as chaves (ex: "conversa_", telefone específico)

        Returns:
            list: Lista de chaves do cache
        """
        try:
            if filtro:
                return [key for key in cache.keys() if filtro in str(key)]
            return list(cache.keys())
        except Exception as e:
            logger.error(f"Erro ao listar chaves do cache: {str(e)}")
            return []

    def deletar_cache_conversa(self, key: str) -> bool:
        """
        Deleta uma conversa específica do cache

        Args:
            key (str): Chave da conversa a ser deletada

        Returns:
            bool: True se deletado com sucesso, False caso contrário
        """
        try:
            if key in cache:
                del cache[key]
                return True
            else:
                logger.warning(f"Chave {key} não encontrada no cache")
                return False
        except Exception as e:
            logger.error(f"Erro ao deletar cache: {str(e)}")
            return False


if __name__ == "__main__":
    import asyncio

    async def main():
        agent = AgentGptalkZap()
        result = await agent.send({"message": "@oficinatech", "model": "1234567897", "sender_name": "João", "sender_phone": "31984198720", "image": "https://www.gptalkzap.com.br/images/logo.png"})
        print(result)
    asyncio.run(main())
