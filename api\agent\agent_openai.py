from openai import OpenAI
from openai import Async<PERSON>penAI
from agents import Agent, Runner, OpenAIChatCompletionsModel, ModelSettings, set_default_openai_key
from openai.types.responses import ResponseTextDeltaEvent
from agents.run import RunConfig
from .agent_secret import Secret
from .agent_logger import Agent<PERSON>ogger
import asyncio
import asyncio
import json
import os
import sys
from .agent_logger import AgentLogger
logger = AgentLogger()

# Adiciona o diretório pai ao path para permitir importações relativas
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from api.agent.agent_secret import Secret

logger = AgentLogger()

class OpenAi:
    def __init__(self):
        #logger.info("@@@@@ OpenAi __init__()")
        self.functions = {}
        self.secret =  Secret()
        self.max_attempts = 3
        self.secret = Secret()
        self.client = OpenAI(api_key= self.secret.OPENAI_API_KEY)
        self.gpt_35_turbo_0125 = 'gpt-3.5-turbo-0125'
        self.gpt_4_turbo= 'gpt-4-turbo'
        self.gt_41 = 'gpt-4.1'
        self.gpt_4_o = 'gpt-4o'
        set_default_openai_key(self.secret.OPENAI_API_KEY)
        #logger.info("openai key set %s" % self.secret.OPENAI_API_KEY)

    def get_client_openAi(self, base_url, api_key,model_name):
        # Linha 28-32
        #logger.info("===== get_client_openAi() =====")
        #logger.info(f"base_url: {base_url}")
        #logger.info(f"api_key: {api_key}")
        #logger.info(f"model_name: {model_name}")
        client = AsyncOpenAI(base_url=base_url, api_key=api_key)
        model =OpenAIChatCompletionsModel(model=model_name, openai_client=client)
        return model



    async def chat(self,message=None,model="gpt-3.5-turbo-0125",llmtype = None,functions=None):
            #print ("\n ##########  agent_openai.py chat() \n")
            #print("message", message)   
            #print("model", model)
            #print("functions", functions)
            #print("llmtype", llmtype)
            #print("functions", functions)


            chat_completion = self.client.chat.completions.create(
                model=model,
                messages=message,
                functions=functions
            )
        
            response = chat_completion
            
            return response

    async def agent_run(self, agent, message):
        logger.info("@@@@@ agent_run chamado com agent=%s e message=%s", type(agent).__name__, message)
        
        try:
            # Configuração do tracing para este run específico
            run_config = RunConfig(
                tracing_disabled=False, # Habilita o tracing para depuração
                workflow_name="Agent Execution",  # Nome do workflow para identificação
            )
            #logger.info(f"run_config: {run_config.__dict__}")
            runner = Runner()
            stream_result =  runner.run_streamed(agent, message, run_config=run_config)
            #logger.info(f"stream_result: {stream_result.__dict__}") 

                    # Itere sobre os eventos do stream
            async for event in stream_result.stream_events():
                # Verifique se o evento é do tipo 'raw_response_event' e se contém um delta de texto
                if event.type == "raw_response_event" and isinstance(event.data, ResponseTextDeltaEvent):
                    # 'event.data.delta' contém o pedaço de texto gerado pela LLM
                    # Você pode enviar isso para o seu frontend imediatamente.
                    # No seu frontend, você concatenaria esses 'deltas' para construir a resposta completa.
                    yield event.data.delta # Retorne o chunk de texto para o cliente

                # Você também pode lidar com outros tipos de eventos, como chamadas de ferramentas, etc.
                # O SDK oferece `RunItemStreamEvent`s para eventos de nível mais alto como "mensagem gerada", "ferramenta executada", etc. [cite: 5]
                # Por exemplo:
                # elif event.type == "run_item_stream_event":
                #     if event.item.type == "tool_call_item":
                #         logger.info(f"Tool was called: {event.item.raw_item.name}")
                #     elif event.item.type == "message_output_item":
                #         logger.info(f"Message output: {ItemHelpers.text_message_output(event.item)}")

            # Após o loop, o stream_result terá o resultado completo (final_output)
            # Você pode acessar o resultado final aqui se precisar fazer algo com ele
            # final_output = stream_result.final_output
            # logger.info(f"Resposta final do agente: {final_output}")

        except Exception as e:
            print(f"Erro durante execução do agente: {str(e)}")
            logger.error(f"Erro durante execução do agente: {str(e)}")
            raise












    async def agent_run_sync (self,agent,message):
       # creating the ai agent

        try:
            runner = Runner()
            result = await runner.run(agent, message)
            return result
        except Exception as e:
            print(f"Erro durante execução síncrona do agente: {str(e)}")
            raise

    async def agent_create(self,name=None,instructions=None,model=None,tools=None,handoff_description=None,handoffs=[],output_type=None,input_guardrails=None,output_guardrails=None,context_class=None , mcp_servers=[]):
        if context_class:
            # Criação do agente com contexto de classe (usando Agent[T])
            agent = Agent[context_class](
                name=name,
                instructions=instructions,
                model=model,
                tools=tools,
                handoff_description=handoff_description,
                handoffs=handoffs,
                output_type=output_type,
                input_guardrails=input_guardrails,
                output_guardrails=output_guardrails,
                mcp_servers=mcp_servers,
                model_settings=ModelSettings(
                    temperature=0.3,
                    top_p=0.9
                )    
            )
        else:
            # Criação do agente sem contexto de classe
            agent = Agent(
                name=name,
                instructions=instructions,
                model=model,
                tools=tools,
                handoff_description=handoff_description,
                handoffs=handoffs,
                output_type=output_type,
                input_guardrails=input_guardrails,
                output_guardrails=output_guardrails,
                mcp_servers=mcp_servers,
                model_settings=ModelSettings(
                    temperature=0.3,
                    top_p=0.9
                )
            )   
        return agent


    async def vision_query(self, data: dict):
        image_url = data.get("url", "")
        prompt = data.get("query", "")
        #print("image_url", image_url)
        #print("prompt", prompt)

        response = self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {"type": "image_url", "image_url": {"url": image_url}}
            ]
        }
    ],
   
)

        return response

    async def transcribe_audio(self, audio_file):
        """
        Transcreve áudio para texto usando OpenAI Whisper
        
        Args:
            audio_file: Arquivo de áudio aberto em modo binário
            
        Returns:
            dict: Resultado da transcrição com o texto
        """
        try:
            transcript = self.client.audio.transcriptions.create(
                model="whisper-1",
                file=audio_file,
                response_format="text"
            )
            return {"text": transcript}
        except Exception as e:
            logger.error(f"Erro na transcrição de áudio: {str(e)}")
            return {"text": ""}

    async def text_to_speech(self, text: str, voice: str = "alloy", model: str = "tts-1"):
        """
        Converte texto para áudio usando OpenAI TTS
        
        Args:
            text (str): Texto para converter
            voice (str): Voz a ser usada (alloy, echo, fable, onyx, nova, shimmer)
            model (str): Modelo TTS (tts-1 ou tts-1-hd)
            
        Returns:
            bytes: Dados do áudio em formato MP3
        """
        try:
            response = self.client.audio.speech.create(
                model=model,
                voice=voice,
                input=text
            )
            return response.content
        except Exception as e:
            logger.error(f"Erro na síntese de voz: {str(e)}")
            return None



class CustomClient:
    def __init__(self, base_url, api_key):
        self.client = AsyncOpenAI(base_url=base_url, api_key=api_key)
        
    def to_dict(self):
        return {
            "base_url": self.client.base_url,
            "api_key": self.client.api_key
        }
        
    @classmethod
    def from_dict(cls, data):
        return cls(data["base_url"], data["api_key"])
async def test_vision_query():
    opi = OpenAi()

    objeto_a_retornar = { 
                      "DOCUMENTO_TIPO": "", 
                      "A_PAGAR": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VALOR":0,
                        "VENCIMENTO": "",
                        "JA_PAGO": "",
                        "DATA_PAGAMENTO": ""
                          } 
                        ]
                      }
                      ,
                      "A_RECEBER":{
                        "TOTAL": 0, 
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VENCIMENTO": "",
                        "JA_RECEBIDO": "",
                        "DATA_PAGAMENTO": ""
                        }
                      ]}
                      ,
                      "DADOS_EMISSOR": { 
                        "RELACAO_TIPO": "",
                        "PESSOA_TIPO": "",
                        "RAZAO_SOCIAL": "",
                        "NOME_FANTASIA": "",
                        "CNPJ": "",
                        "CPF": "",
                        "TELEFONE": "",
                        "ENDEREÇO": { 
                            "CEP": "",
                            "LOGRADOURO": "",
                            "NUMERO": "",
                            "COMPLEMENTO": "",
                            "BAIRRO": "",
                            "CIDADE": "",
                            "UF": ""
                        }
                      },
                      "DISCRIMINAÇÃO": ""
                    }


    objeto_exemplo = { 
                      "DOCUMENTO_TIPO": "3-Conta de água", 
                      "A_PAGAR": {
                          "TOTAL": 100.00,
                          "NR_PARCELAS": 1,
                          "PARCELAS": [{
                          "VALOR": 100.00,
                          "VENCIMENTO": "01/04/25",
                          "JA_PAGO": "N",
                          "DATA_PAGAMENTO": ""
                          }]
                        },
                      "A_RECEBER": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS": []
                      },
                      "DADOS_EMISSOR": { 
                          "RELACAO_TIPO": "2 - Fornecedor",
                          "PESSOA_TIPO": "1 - Jurídica",
                          "RAZAO_SOCIAL": "COPASA ABASTECIMENTOS S/A",
                          "NOME_FANTASIA": "COPASA",
                          "CNPJ": "37.753.978/0001-03",
                          "CPF": "",
                          "TELEFONE": "(31)984784825",
                          "ENDEREÇO": { 
                              "CEP": "31910720",
                              "LOGRADOURO": "RUA LIDIA",
                              "NUMERO": "125",
                              "COMPLEMENTO": "",
                              "BAIRRO": "CENTRO",
                              "CIDADE": "BELO HORIZONTE",
                              "UF": "MG"
                          }
                      },
                      "DISCRIMINAÇÃO": "CONTA DE MARÇO/25"
                    }


    data = {
            #"url": "http://localhost/gptalk/negocios/0068108573/documentos/contabilidade/2305562857.pdf",
            "url": "https://gptalk.com.br/negocios/0068108573/imagens/contabilidade/1234567890.jpg",
            #"url": "https://gptalk.com.br/imagens/logo.png",
            "query": f"""
                    Preciso que analise o documento na imagem e preecha e retorne o objeto abaixo com os dados possiveis e encontrados no documento. Utilize as tabelas auxiliares para preecher 
                    
                    TABELAS AUXILIARES PARA CRIAÇÃO DO OBJETO
                    
                    Tabela para DOCUMENTO_TIPO:
                        1-boleto
                        2-Conta de Luz
                        3-Conta de água
                        4-Conta de internet
                        5-nota fiscal de compra de mercadorias para revenda
                        6-nota fiscal de serviços recebidos
                        7-nota fiscal de compra de compra de móveis
                        8-nota fiscal de compra de compra de equipamentos
                    	9-Contrato de prestação de serviço
                    	10-Recibo
                    	11-Cupom Fiscal    
                        
                        RELACAO_TIPO:
                        1 - Cliente
                        2 - Fornecedor
                        3 -Funcionário
                        4 - Sócio ou proprietário
                        
                        PESSOA_TIPO
                        1 - Jurídica
                        2 - Física
                        
                     OBSERVAÇÕES:
                     
                     Se nas chaves A_PAGAR ou  A_RECEBER não houverem dados, o valos deverá ser um array vazio: [] 
                     
                     **IMPORTANTE**: A resposta DEVE ser uma string JSON válida, utilizando aspas duplas (") para todas as chaves e valores de string. NÃO use aspas simples (').
                     
                     objeto a ser retornado:
                     {objeto_a_retornar}  
                      
                    
                    Exemplo do objto preenchido com os dados da imagem de uma conta de água (JSON válido):

                    {objeto_exemplo}

                    Em caso de erro, não retorne o objeto de dados. Retorne apenas este:
                    {{"erro": "Informe aqui o numero e a mensagem do erro ocorrido"}}
                """

        }
    
    result = await opi.vision_query(data)
    return result



if __name__ == "__main__":



    async def test_agent_google():
        print("\n ##########  agent_openai.py test_agent_google() \n")
        agent1 = OpenAi()
        secret = Secret()
        base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        model_name = ""
        api_key = secret.get_secret("GOOGLE_API_KEY")
        logger.info(f"api_key: {api_key}")
        logger.info(f"base_url: {base_url}")
        logger.info(f"model_name: {model_name}")
        client = agent1.get_client_openAi(base_url=base_url, api_key=api_key,model_name=model_name)
        logger.info(f"client: {client}")


        


    asyncio.run(test_agent_google())



