#geral
import asyncio 
import json
from urllib.parse import urlparse, parse_qs
from youtube_transcript_api import YouTubeTrans<PERSON><PERSON><PERSON>
from pytube import YouTube

#local
#from text import count_tokens_openai, summarizer_gemini, summarizer_claude, summarizer_openai,translator_gemini, detect_idiom
#from mysql_functions import add_data

#app
from ..functions.text import count_tokens_openai, summarizer_gemini, summarizer_claude, summarizer_openai,translator_gemini, detect_idiom
from ..functions.programming.database.my_sql import mysql_query,mysql_add_data


# @title youtube_getSummarization_map
#youtube_getSummarization
#https://colab.research.google.com/drive/1Iw7EPIMjafxzKSVAuwaMQCfCuY4JyTDC#scrollTo=s-T9ZGtRi7kX&line=1&uniqifier=1

#=======================================================
youtube_getSummarization_map = {
    "name":"youtube_getSummarization",
    "description":
    " O objetivo desta função é obter a sumarização  de um video do youtube."
    "Deverá ser retornado um objeto com as propriedades: resumo, titulo."
    "O resumo deverá conter o titulo do video na primeira linha, ja traduzido   para o idioma solicitado, e a sumarização do video na segunda linha. Não incluir a url do video na resposta."
    
    ,
    "parameters" : {
        "type": "object",
        "properties": {
            "url": {
                "type": "string",
                "description" : "url válida de um video do youtube",
             },
            "idiom": {
                "type": "string",
                "description" : "idioma para tradução do titulo e da sumarização pela função."
                "se não informado, o titulo e a sumarização serão retornados no idioma original e assim devem ser utilizados,"
                "sem nenhuma tradução adicional",
             },
            },
          "required": ["url"],
       }

}
#==================================================================
def is_json_valid(s):
    try:
        json_object = json.loads(s)
    except ValueError as e:
        return False
    return True
#=======================================================
async def youtube_getSummarization (args):
 print("----- youtube_getSummarization -----")
  #print("args",args)
 response = ""
 summary = ""
 idiom = ""
 if "idiom" in args:
  idiom = args["idiom"]
  
 
 useCredits = args["useCredits"]
 credits = args["credits"]
 print("useCredits",useCredits)
 print("credits",credits)
 
 print("idiom",idiom)
  #print("useCredits",useCredits)
  #print("credits",credits)
  #print("url",url)
  #print("videoID",videoID)
  #print("transcript",transcript);
  #print("textTranscript",textTranscript);
  #print("totalTokens",totalTokens);
  #print("response",response)

 url = args["url"]
 videoID = youtube_get_video_ID(url)

 title, thumbnail_url , description = await youtube_info(url)
 print("url",url)
 print("videoID",videoID)
 print(f"Título do vídeo: {title}")
 print(f"URL da thumbnail: {thumbnail_url}")

 transcript = await youtube_getTranscript(videoID)
 #print("transcript",transcript);
 textTranscript = ' '.join(item['text'] for item in transcript)
 #print("textTranscript",textTranscript);
 totalTokens  = count_tokens_openai(textTranscript)
 print("totalTokens",totalTokens);

 if useCredits==True:
  print("useCredits neste momento é true ",useCredits)
  if credits < totalTokens:
   response = "Creditos insuficientes para sumarizar o video"
   print("creditos insuficientes")
   return response

  if totalTokens <= 110000:
         print("vou sumarizar usando a openai")
         # Se o número total de tokens for menor ou igual a 110.000
         # Utiliza a função summarizer_openai para gerar o resumo
         summary = await summarizer_openai(textTranscript, 2)
  else:
         print("vou sumarizar usando o claude")
         # Se o número total de tokens for maior que 110.000
         # Utiliza a função summarizer_claude para gerar o resumo
         summary = await summarizer_claude(textTranscript, 2)
 else:
    
     print("nao usar credito")
     # Se a variável usarCredito for falsa
     if totalTokens <= 20000:
         print("vou sumarizar usando a gemini")

         # Se o número total de tokens for menor ou igual a 20.000
         # Utiliza a função summarizer_gemini para gerar o resumo
         summary = await summarizer_gemini(textTranscript, 2)
     else:
        print
        # Se o número total de tokens for maior que 20.000
        # Utiliza a função summarizer_claude para gerar o resumo
        summary = await summarizer_claude(textTranscript, 2)

 print("Summary",summary)

 if(is_json_valid(summary)):
  #print("summary json  valido")
  summary =  json.loads(summary)
  summary["titulo"] = title
 else: 
  print("summary json  invalido")
  response = "Erro ao sumarizar o video. Solicite novamente"
  return response

 if idiom:
  #print("idiom",args["idiom"])
  #print("vou traduzir o summary")
  if idiom != detect_idiom(summary["resumo"]):
   
   summary["resumo"] = await translator_gemini(summary["resumo"],args["idiom"])
   #print("summary traduzido",summary["resumo"])
  if idiom != detect_idiom(summary["titulo"]):
   summary["titulo"] = await translator_gemini(summary["titulo"],args["idiom"])
   #print("titulo traduzido",summary["titulo"])
 print("description", description)
 conteudo = {
      "USUARIO_ID": args["usuario_id"],
        "CONTEUDO_ID": videoID,
        "TITULO": title,
        "DESCRICAO": description,
        "RESUMO": summary["titulo"] + "\n" + summary["resumo"],
        "INTEGRA": textTranscript,
        "TRANSCRICAO": json.dumps(transcript),
        "URL": url,
        "TIPO_ID": 1,
        "CAPA_URL": thumbnail_url
 }
 
 id_conteudo = mysql_add_data(conteudo, "CONTEUDO")
 print ("id_conteudo",id_conteudo)

 return "** Titulo:" + summary["titulo"] + "** " + "\n" +  summary["resumo"]

#========================""===============================
def youtube_get_video_ID(url):
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)

    if 'v' in query_params:
        video_id = query_params['v'][0]
        return video_id
    else:
        return None
    
#=======================================================
async def youtube_getTranscript(videoID):
  print("----- youtube_getTranscript -----" , videoID)
  global transcript, textTranscript
  transcript = YouTubeTranscriptApi.get_transcript(videoID)
  #textTranscript = ' '.join(item['text'] for item in transcript)
  return transcript


#=======================================================
async def youtube_info(url):
    #print("----- youtube_info -----", url)
    try:
        # Criar um objeto YouTube com a URL do vídeo
        video = YouTube(url)
        print("video",video)
        
        # Obter o título do vídeo
        title = video.title
        
        # Obter a URL da imagem da capa (thumbnail)
        thumbnail_url = video.thumbnail_url

        description = video.description
        #print("video.description",video.description)
        
        return title, thumbnail_url, description
    except Exception as e:
        print(f"Ocorreu um erro: {e}")
        return None, None, None

#=======================================================
async def youtube_data(link):
    print("youtube_data", youtube_data)
    try:
        # Cria um objeto YouTube passando o link do vídeo
        yt = YouTube(link)

        # Obtém o título do vídeo
        title = yt.title
        print("title",title)
        # Obtém o link da miniatura do vídeo
        link_thumb = yt.thumbnail_url
        print("link_thumb", link)
        return {'titulo':title,'capa': link_thumb}
    except Exception as e:
        return {'erro': str(e)}


#=======================================================
async def main():
  print("----- main() -----")
  args = {}
  args["url"] = "https://www.youtube.com/watch?v=4CZLASQsQgw"
  args["idiom"] = "espanhol"
  args["useCredits"] = False
  args["credits"] = 10
  args["usuario_id"] = "x1z2y3"
  await youtube_getSummarization(args)

