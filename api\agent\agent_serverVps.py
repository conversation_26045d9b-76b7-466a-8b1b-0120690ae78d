#==========================================
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from .agent_logger import AgentLogger
from .agent_secret import Secret
from .agent_llm import LLM
from .agent_openai import OpenAi
from .agent_message import Message
from ..cache import cache
from ..functions.util import generate_unique_id
from agents import function_tool
from datetime import datetime
from threading import Lock
import paramiko
import subprocess
import os
import pytz
import json
from typing import Tuple, Optional

logger = AgentLogger()
router = APIRouter()
secret = Secret()
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
oai = OpenAi()

github_user = secret.get_secret("GITHUB_USER")
github_token = secret.get_secret("GITHUB_TOKEN")

# Configurações do VPS
vps_host = secret.get_secret("VPS_HOST")
vps_port_str = secret.get_secret("VPS_PORT")
vps_port = int(vps_port_str) if vps_port_str else 22
vps_user = secret.get_secret("VPS_USER")
vps_password = secret.get_secret("VPS_PASSWORD")  # ou usar chave SSH


appsOnserver = {
    "6729479663": {
        "name": "Gptalk Server",
        "description": "Servidor (backend)",
        "root": "/var/www/gptalk-server",
        "url": "https://server.gptalk.com.br",
        "service_name": "gptalk-server"
    },
    "8387520375": {
        "name": "Gptalk App Phj",
        "description": "Servidor (backend)",
        "root": "/var/www/html/app_phj",
        "url": "https://app.gptalk.com.br/phj"
    },
    "4961585763": {
        "name": "Gptalk Site",
        "description": "Site do projeto GPTalk)",
        "root": "/var/www/html",
        "url": "https://gptalk.com.br"
    }
}


class AppConfig:
    """Classe para gerenciar configurações dos apps e credenciais GitHub"""
    
    def __init__(self):
        self.apps = appsOnserver
        self.github_user = github_user
        self.github_token = github_token
        self.vps_host = vps_host
        self.vps_port = vps_port
        self.vps_user = vps_user
        self.vps_password = vps_password
    
    def get_app_by_id(self, app_id: str) -> dict:
        """Retorna informações do app pelo ID"""
        return self.apps.get(app_id)
    
    def app_exists(self, app_id: str) -> bool:
        """Verifica se o app_id existe"""
        return app_id in self.apps
    
    def get_app_root(self, app_id: str) -> str:
        """Retorna o diretório root do app"""
        app = self.get_app_by_id(app_id)
        return app["root"] if app else None
    
    def get_app_name(self, app_id: str) -> str:
        """Retorna o nome do app"""
        app = self.get_app_by_id(app_id)
        return app["name"] if app else None
    
    def get_all_apps(self) -> dict:
        """Retorna todos os apps disponíveis"""
        return self.apps
    
    def get_github_credentials(self) -> tuple:
        """Retorna tupla com (usuário, token) do GitHub"""
        return (self.github_user, self.github_token)
    
    def validate_github_credentials(self) -> bool:
        """Valida se as credenciais GitHub estão configuradas"""
        return bool(self.github_user and self.github_token)
    
    def get_vps_credentials(self) -> dict:
        """Retorna credenciais do VPS sempre atualizadas do secret"""
        return {
            "host": secret.get_secret("VPS_HOST"),
            "port": int(secret.get_secret("VPS_PORT") or 22),
            "user": secret.get_secret("VPS_USER"),
            "password": secret.get_secret("VPS_PASSWORD")
        }
    
    def validate_vps_credentials(self) -> bool:
        """Valida se as credenciais do VPS estão configuradas"""
        return bool(self.vps_host and self.vps_user and self.vps_password)


class AgentServerVps:
    def install_package(self, package_command: str, app_id: str = None, directory: str = None) -> dict:
        """
        Instala um pacote ou executa um comando de instalação no servidor via SSH.
        Args:
            package_command: Comando de instalação (ex: apt install, wget, etc)
            app_id: ID do app para obter diretório base (opcional)
            directory: Diretório onde executar o comando (opcional)
        Returns:
            Dict com resultado da instalação
        """
        try:
            if app_id and not self.config.app_exists(app_id):
                return {"success": False, "message": f"App ID '{app_id}' não encontrado"}
            app_root = self.config.get_app_root(app_id) if app_id else directory
            sucesso, stdout, stderr = self.ssh_execute(package_command, app_root)
            if sucesso:
                return {"success": True, "message": f"Comando '{package_command}' executado com sucesso.", "output": stdout}
            else:
                return {"success": False, "message": f"Erro ao executar '{package_command}': {stderr}", "output": stdout, "error": stderr}
        except Exception as e:
            return {"success": False, "message": f"Erro inesperado: {str(e)}"}

    def __init__(self):
        self.config = AppConfig()

    def ssh_execute(self, command: str, directory: str = None) -> Tuple[bool, str, str]:
        """
        Executa um comando SSH no VPS
        
        Args:
            command: Comando a ser executado
            directory: Diretório onde executar o comando (opcional)
            
        Returns:
            Tupla com (sucesso, stdout, stderr)
        """
        try:
            # Validar credenciais VPS
            if not self.config.validate_vps_credentials():
                return False, "", "Credenciais do VPS não configuradas"
            
            # Obter credenciais
            vps_creds = self.config.get_vps_credentials()
            
            # Criar conexão SSH
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            logger.info(f"Conectando ao VPS: {vps_creds['host']}:{vps_creds['port']}")
            
            # Conectar ao VPS
            ssh.connect(
                hostname=vps_creds["host"],
                port=vps_creds["port"],
                username=vps_creds["user"],
                password=vps_creds["password"],
                timeout=30
            )
            
            # Preparar comando com mudança de diretório se necessário
            if directory:
                full_command = f"cd {directory} && {command}"
            else:
                full_command = command
            
            logger.info(f"Executando comando: {full_command}")
            
            # Executar comando
            stdin, stdout, stderr = ssh.exec_command(full_command)
            
            # Obter resultados
            stdout_text = stdout.read().decode('utf-8')
            stderr_text = stderr.read().decode('utf-8')
            exit_code = stdout.channel.recv_exit_status()
            
            # Fechar conexão
            ssh.close()
            
            # Log do resultado
            if exit_code == 0:
                logger.info(f"Comando executado com sucesso: {stdout_text}")
                return True, stdout_text, stderr_text
            else:
                logger.error(f"Erro ao executar comando: {stderr_text}")
                return False, stdout_text, stderr_text
                
        except paramiko.AuthenticationException:
            error_msg = "Falha na autenticação SSH"
            logger.error(error_msg)
            return False, "", error_msg
            
        except paramiko.SSHException as e:
            error_msg = f"Erro SSH: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg
            
        except Exception as e:
            error_msg = f"Erro inesperado: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def git_pull_app(self, app_id: str) -> Tuple[bool, str, str]:
        """
        Executa git pull no app especificado
        
        Args:
            app_id: ID do app a ser atualizado
            
        Returns:
            Tupla com (sucesso, mensagem_sucesso, mensagem_erro)
        """
        try:
            # Validar se o app existe
            if not self.config.app_exists(app_id):
                error_msg = f"App ID '{app_id}' não encontrado"
                logger.error(error_msg)
                return False, "", error_msg
            
            # Validar credenciais GitHub
            if not self.config.validate_github_credentials():
                error_msg = "Credenciais GitHub não configuradas"
                logger.error(error_msg)
                return False, "", error_msg
            
            # Obter informações do app
            app_name = self.config.get_app_name(app_id)
            app_root = self.config.get_app_root(app_id)
            github_user, github_token = self.config.get_github_credentials()
            
            logger.info(f"Iniciando git pull para: {app_name} (ID: {app_id})")
            logger.info(f"Diretório: {app_root}")
            
            # Verificar se o diretório existe e é um repositório git
            check_git_cmd = "test -d .git && echo 'Git repo found' || echo 'Not a git repo'"
            success, stdout, stderr = self.ssh_execute(check_git_cmd, app_root)
            
            if not success:
                error_msg = f"Erro ao verificar repositório git: {stderr}"
                logger.error(error_msg)
                return False, "", error_msg
            
            if "Not a git repo" in stdout:
                error_msg = f"Diretório {app_root} não é um repositório git"
                logger.error(error_msg)
                return False, "", error_msg
            
            # Configurar git com credenciais temporariamente
            git_config_cmd = f'git config --local credential.helper "!f() {{ echo username={github_user}; echo password={github_token}; }}; f"'
            success, stdout, stderr = self.ssh_execute(git_config_cmd, app_root)
            
            if not success:
                logger.warning(f"Aviso ao configurar credenciais git: {stderr}")
            
            # Detectar branch principal (main ou master)
            detect_branch_cmd = "git branch --show-current"
            branch_success, branch_stdout, branch_stderr = self.ssh_execute(detect_branch_cmd, app_root)
            
            if branch_success and branch_stdout.strip():
                current_branch = branch_stdout.strip()
                logger.info(f"Branch atual detectada: {current_branch}")
            else:
                # Fallback: tentar detectar branch remota padrão
                remote_branch_cmd = "git symbolic-ref refs/remotes/origin/HEAD | sed 's@^refs/remotes/origin/@@'"
                remote_success, remote_stdout, remote_stderr = self.ssh_execute(remote_branch_cmd, app_root)
                current_branch = remote_stdout.strip() if remote_success and remote_stdout.strip() else "main"
                logger.info(f"Branch padrão detectada: {current_branch}")
            
            # Executar git pull com a branch correta
            git_pull_cmd = f"git pull origin {current_branch}"
            success, stdout, stderr = self.ssh_execute(git_pull_cmd, app_root)
            
            # Limpar configuração de credenciais
            cleanup_cmd = "git config --local --unset credential.helper"
            self.ssh_execute(cleanup_cmd, app_root)
            
            if success:
                success_msg = f"Git pull executado com sucesso para {app_name}"
                logger.info(f"{success_msg}. Output: {stdout}")
                return True, success_msg, stdout
            else:
                error_msg = f"Erro ao executar git pull para {app_name}: {stderr}"
                logger.error(error_msg)
                return False, "", error_msg
                
        except Exception as e:
            error_msg = f"Erro inesperado no git pull: {str(e)}"
            logger.error(error_msg)
            return False, "", error_msg

    def get_git_info(self, app_id: str) -> dict:
        """
        Obtém informações detalhadas do Git para um app
        
        Args:
            app_id: ID do app
            
        Returns:
            Dict com informações do Git
        """
        try:
            if not self.config.app_exists(app_id):
                return {"error": f"App ID '{app_id}' não encontrado"}
            
            app_name = self.config.get_app_name(app_id)
            app_root = self.config.get_app_root(app_id)
            
            # Verificar se é um repositório Git
            is_git_cmd = "test -d .git && echo 'Git repo' || echo 'Not a git repo'"
            success, stdout, stderr = self.ssh_execute(is_git_cmd, app_root)
            
            if not success or "Not a git repo" in stdout:
                return {
                    "error": f"Diretório {app_root} não é um repositório Git",
                    "is_git_repo": False
                }
            
            # Obter informações do Git
            info = {
                "app_name": app_name,
                "app_id": app_id,
                "directory": app_root,
                "is_git_repo": True
            }
            
            # Branch atual
            current_branch_cmd = "git branch --show-current"
            success, stdout, stderr = self.ssh_execute(current_branch_cmd, app_root)
            info["current_branch"] = stdout.strip() if success else "unknown"
            
            # Listar branches locais
            local_branches_cmd = "git branch"
            success, stdout, stderr = self.ssh_execute(local_branches_cmd, app_root)
            info["local_branches"] = [b.strip().replace('* ', '') for b in stdout.split('\n') if b.strip()] if success else []
            
            # Listar branches remotas
            remote_branches_cmd = "git branch -r"
            success, stdout, stderr = self.ssh_execute(remote_branches_cmd, app_root)
            info["remote_branches"] = [b.strip() for b in stdout.split('\n') if b.strip() and not 'HEAD' in b] if success else []
            
            # Verificar remote origin
            remote_url_cmd = "git remote get-url origin"
            success, stdout, stderr = self.ssh_execute(remote_url_cmd, app_root)
            info["remote_origin"] = stdout.strip() if success else "not configured"
            
            # Status do repositório
            status_cmd = "git status --porcelain"
            success, stdout, stderr = self.ssh_execute(status_cmd, app_root)
            info["has_changes"] = bool(stdout.strip()) if success else False
            
            return info
            
        except Exception as e:
            return {"error": f"Erro inesperado: {str(e)}"}

    def get_app_status(self, app_id: str) -> dict:
        """
        Obtém status do repositório git do app
        
        Args:
            app_id: ID do app
            
        Returns:
            Dict com informações do status
        """
        try:
            if not self.config.app_exists(app_id):
                return {"error": f"App ID '{app_id}' não encontrado"}
            
            app_name = self.config.get_app_name(app_id)
            app_root = self.config.get_app_root(app_id)
            
            # Verificar status do git
            status_cmd = "git status --porcelain && echo '---SEPARATOR---' && git log --oneline -5"
            success, stdout, stderr = self.ssh_execute(status_cmd, app_root)
            
            if not success:
                return {"error": f"Erro ao obter status: {stderr}"}

            # logger.info("@@@@@@@ stdout: %s", stdout)   
            # Separar status e log
            parts = stdout.split("---SEPARATOR---")
            git_status = parts[0].strip() if len(parts) > 0 else ""
            git_log = parts[1].strip() if len(parts) > 1 else ""
            
            return {
                "app_name": app_name,
                "app_id": app_id,
                "directory": app_root,
                "git_status": git_status if git_status else "Working directory clean",
                "recent_commits": git_log.split('\n') if git_log else []
            }
            
        except Exception as e:
            return {"error": f"Erro inesperado: {str(e)}"}

    def check_remote_updates(self, app_id: str) -> dict:
        """
        Verifica se há atualizações no repositório remoto (GitHub) que ainda não foram aplicadas no servidor
        
        Args:
            app_id: ID do app
            
        Returns:
            Dict com informações sobre atualizações pendentes
        """
        try:
            if not self.config.app_exists(app_id):
                return {"error": f"App ID '{app_id}' não encontrado"}
            
            app_name = self.config.get_app_name(app_id)
            app_root = self.config.get_app_root(app_id)
            
            # Validar credenciais GitHub
            if not self.config.validate_github_credentials():
                logger.warning("Credenciais GitHub não configuradas - continuando com fetch sem credenciais")
                github_user, github_token = None, None
            else:
                github_user, github_token = self.config.get_github_credentials()
            
            # 1. Configurar credenciais Git temporariamente (se disponíveis)
            if github_user and github_token:
                git_config_cmd = f'git config --local credential.helper "!f() {{ echo username={github_user}; echo password={github_token}; }}; f"'
                config_success, config_stdout, config_stderr = self.ssh_execute(git_config_cmd, app_root)
                
                if not config_success:
                    logger.warning(f"Aviso ao configurar credenciais git: {config_stderr}")
            
            # 2. Fazer git fetch para obter informações atualizadas do remoto
            logger.info(f"Fazendo git fetch para obter informações atualizadas do {app_name}")
            fetch_cmd = "git fetch origin"
            fetch_success, fetch_stdout, fetch_stderr = self.ssh_execute(fetch_cmd, app_root)
            
            # 3. Limpar configuração de credenciais
            if github_user and github_token:
                cleanup_cmd = "git config --local --unset credential.helper"
                self.ssh_execute(cleanup_cmd, app_root)
            
            if not fetch_success:
                logger.warning(f"Git fetch falhou: {fetch_stderr}")
                # Continuar mesmo se fetch falhar, mas avisar que dados podem estar desatualizados
            
            # 4. Detectar branch atual
            current_branch_cmd = "git branch --show-current"
            branch_success, branch_stdout, branch_stderr = self.ssh_execute(current_branch_cmd, app_root)
            current_branch = branch_stdout.strip() if branch_success else "main"
            
            # 5. Verificar se há commits no remoto que não estão no local
            compare_cmd = f"git log HEAD..origin/{current_branch} --oneline"
            compare_success, compare_stdout, compare_stderr = self.ssh_execute(compare_cmd, app_root)
            
            # 6. Verificar status local (arquivos modificados)
            status_cmd = "git status --porcelain"
            status_success, status_stdout, status_stderr = self.ssh_execute(status_cmd, app_root)
            
            # 7. Obter commits locais recentes para referência
            local_commits_cmd = "git log --oneline -5"
            local_success, local_stdout, local_stderr = self.ssh_execute(local_commits_cmd, app_root)
            
            # 8. Obter commits remotos recentes para referência
            remote_commits_cmd = f"git log origin/{current_branch} --oneline -5"
            remote_success, remote_stdout, remote_stderr = self.ssh_execute(remote_commits_cmd, app_root)
            
            # Analisar resultados
            has_remote_updates = bool(compare_stdout.strip()) if compare_success else False
            has_local_changes = bool(status_stdout.strip()) if status_success else False
            
            pending_commits = []
            if has_remote_updates and compare_stdout.strip():
                pending_commits = [line.strip() for line in compare_stdout.strip().split('\n') if line.strip()]
            
            local_commits = []
            if local_success and local_stdout.strip():
                local_commits = [line.strip() for line in local_stdout.strip().split('\n') if line.strip()]
            
            remote_commits = []
            if remote_success and remote_stdout.strip():
                remote_commits = [line.strip() for line in remote_stdout.strip().split('\n') if line.strip()]
            
            return {
                "app_name": app_name,
                "app_id": app_id,
                "directory": app_root,
                "current_branch": current_branch,
                "has_remote_updates": has_remote_updates,
                "has_local_changes": has_local_changes,
                "pending_commits_count": len(pending_commits),
                "pending_commits": pending_commits,
                "local_commits": local_commits,
                "remote_commits": remote_commits,
                "fetch_success": fetch_success,
                "fetch_output": fetch_stdout if fetch_success else fetch_stderr,
                "needs_deploy": has_remote_updates,
                "status_summary": self._get_status_summary(has_remote_updates, has_local_changes, pending_commits, fetch_success)
            }
            
        except Exception as e:
            return {"error": f"Erro inesperado: {str(e)}"}
    
    def _get_status_summary(self, has_remote_updates: bool, has_local_changes: bool, pending_commits: list, fetch_success: bool = True) -> str:
        """
        Gera um resumo do status para facilitar a interpretação
        """
        if not fetch_success:
            base_msg = "⚠️ Git fetch falhou - dados podem estar desatualizados. "
        else:
            base_msg = ""
            
        if has_remote_updates and has_local_changes:
            return f"{base_msg}⚠️ Há {len(pending_commits)} commits no GitHub para deploy E arquivos modificados localmente"
        elif has_remote_updates:
            return f"{base_msg}🔄 Há {len(pending_commits)} commits no GitHub esperando deploy"
        elif has_local_changes:
            return f"{base_msg}📝 Há arquivos modificados localmente (mas sem commits pendentes do GitHub)"
        else:
            return f"{base_msg}✅ Servidor está atualizado com o GitHub"

    def get_service_status(self, app_id: str) -> dict:
        """
        Verifica o status do serviço systemd do app
        
        Args:
            app_id: ID do app
            
        Returns:
            Dict com status do serviço
        """
        try:
            if not self.config.app_exists(app_id):
                return {"error": f"App ID '{app_id}' não encontrado"}
            
            app_name = self.config.get_app_name(app_id)
            
            # Mapear app_id para nome do serviço
            service_name = None
            if app_id == "6729479663":
                service_name = "gptalk-server"
            
            if not service_name:
                return {"error": f"Nenhum serviço configurado para app {app_id}"}
            
            # Verificar status do serviço
            status_cmd = f"systemctl status {service_name} --no-pager -l"
            success, stdout, stderr = self.ssh_execute(status_cmd)
            
            # Verificar se está ativo
            active_cmd = f"systemctl is-active {service_name}"
            active_success, active_stdout, active_stderr = self.ssh_execute(active_cmd)
            
            # Verificar se está habilitado
            enabled_cmd = f"systemctl is-enabled {service_name}"
            enabled_success, enabled_stdout, enabled_stderr = self.ssh_execute(enabled_cmd)
            
            # Obter logs recentes
            logs_cmd = f"journalctl -u {service_name} --no-pager -n 20"
            logs_success, logs_stdout, logs_stderr = self.ssh_execute(logs_cmd)
            
            return {
                "app_name": app_name,
                "app_id": app_id,
                "service_name": service_name,
                "is_active": active_stdout.strip() if active_success else "unknown",
                "is_enabled": enabled_stdout.strip() if enabled_success else "unknown",
                "status_output": stdout if success else stderr,
                "recent_logs": logs_stdout.split('\n')[-10:] if logs_success else ["Erro ao obter logs"],
                "status_success": success
            }
            
        except Exception as e:
            return {"error": f"Erro inesperado: {str(e)}"}

    def install_dependencies(self, app_id: str) -> dict:
        """
        Instala as dependências Python do app
        
        Args:
            app_id: ID do app
            
        Returns:
            Dict com resultado da instalação
        """
        try:
            if not self.config.app_exists(app_id):
                return {"error": f"App ID '{app_id}' não encontrado"}
            
            app_name = self.config.get_app_name(app_id)
            app_root = self.config.get_app_root(app_id)
            
            # Verificar se existe requirements.txt
            check_requirements = "test -f requirements.txt && echo 'Found' || echo 'Not found'"
            success, stdout, stderr = self.ssh_execute(check_requirements, app_root)
            
            if not success:
                return {"error": f"Erro ao verificar requirements.txt: {stderr}"}
            
            # Ativar ambiente virtual e instalar dependências
            if "Found" in stdout:
                install_cmd = ". venv/bin/activate && pip install -r requirements.txt"
                success, stdout, stderr = self.ssh_execute(install_cmd, app_root)
                
                if success:
                    return {
                        "success": True,
                        "message": f"Dependências instaladas com sucesso para {app_name}",
                        "output": stdout
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Erro ao instalar dependências para {app_name}",
                        "error": stderr
                    }
            else:
                # Instalar apenas paramiko (que está faltando)
                install_cmd = ". venv/bin/activate && pip install paramiko"
                success, stdout, stderr = self.ssh_execute(install_cmd, app_root)
                
                if success:
                    return {
                        "success": True,
                        "message": f"Paramiko instalado com sucesso para {app_name}",
                        "output": stdout
                    }
                else:
                    return {
                        "success": False,
                        "message": f"Erro ao instalar paramiko para {app_name}",
                        "error": stderr
                    }
            
        except Exception as e:
            return {"error": f"Erro inesperado: {str(e)}"}


# Instância global do agente
agent = AgentServerVps()


# ===============================================================================
# CLASSE AGENTE CONVERSACIONAL PARA SERVIDOR VPS
# ===============================================================================

class AgentServerVpsConversacional:
    def __init__(
        self,
        name="server-vps",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None):
        logger.info("===== AgentServerVpsConversacional() =====")
        logger.info(f"name: {name}")
        logger.info(f"usuario_nome: {usuario_nome}")
        logger.info(f"usuario_idx: {usuario_idx}")
        logger.info(f"negocio_idx: {negocio_idx}")
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx
        
        # Converter dados dos apps para formato mais legível
        apps_info = ""
        for app_id, app_data in appsOnserver.items():
            apps_info += f"""
📱 **{app_data['name']}** (ID: {app_id})
   - Descrição: {app_data['description']}
   - Diretório: {app_data['root']}
   - URL: {app_data['url']}
   - Serviço: {app_data.get('service_name', 'N/A')}
"""
        
        self.instructions = f"""
        Você é um assistente especializado em gerenciamento de servidores VPS que ajuda a:
        1. **Fazer deploy de aplicações** via git pull
        2. **Verificar status** de aplicações e serviços  
        3. **Gerenciar dependências** Python
        4. **Monitorar logs** e saúde dos serviços
        5. **Executar comandos** via SSH no servidor
        6. **Instalar pacotes ou extensões** usando a função de instalação dedicada
        7. **Executar comandos administrativos gerais** usando a função `comandos_gerais()` para qualquer solicitação não prevista nas funções acima

        ## 🛠️ **Ferramentas Disponíveis:**

        ### `listar_aplicativos()`
        - Lista todos os aplicativos disponíveis no servidor
        - Mostra ID, nome, descrição, diretório e URL de cada app

        ### `verificar_atualizacoes_github(app_id)`
        - **PRINCIPAL FERRAMENTA** para verificar deploys pendentes do GitHub
        - Configura credenciais Git e faz git fetch automaticamente
        - Compara repositório local vs remoto para detectar commits novos
        - **SEMPRE use esta função quando perguntarem sobre deploy pendente**
        - Formato: verificar_atualizacoes_github("Gptalk Server") ou verificar_atualizacoes_github("6729479663")

        ### `testar_correcao_github(app_id)`
        - **FUNÇÃO DE DIAGNÓSTICO** para testar se a correção do git fetch está funcionando
        - Verifica credenciais GitHub e VPS
        - Testa conexão e autenticação com GitHub
        - Use quando houver problemas de autenticação ou para confirmar que correções funcionaram

        ### `verificar_status_app(app_id)`
        - Verifica status do repositório Git de um aplicativo (apenas local)
        - Mostra mudanças pendentes e commits recentes locais
        - Use o ID do app ou pode identificar pelo nome usando listar_aplicativos()

        ### `verificar_status_servico(app_id)`
        - Verifica status do serviço systemd (se aplicável)
        - Mostra se está ativo, habilitado e logs recentes
        - Útil para diagnosticar problemas de execução

        ### `fazer_deploy_app(app_id)`
        - Executa git pull + restart do serviço (se aplicável)
        - Faz deploy completo de uma aplicação
        - Detecta automaticamente a branch correta (main/master)

        ### `instalar_dependencias_app(app_id)`
        - Instala dependências Python via pip
        - Ativa ambiente virtual antes da instalação
        - Útil quando há novos pacotes no requirements.txt

        ### `verificar_informacoes_git(app_id)`
        - Obtém informações detalhadas do repositório Git
        - Mostra branches, remotes, status do repositório

        ### `executar_comando_ssh(comando, diretorio)`
        - Executa comando personalizado via SSH no servidor
        - Use com cuidado - pode afetar o sistema
        - Sempre explicar o que o comando fará antes de executar

        ### `instalar_aplicativo(comando_instalacao, app_id=None, diretorio=None)`
        - Executa comandos de instalação de pacotes, extensões ou utilitários no servidor via SSH
        - Use esta função sempre que o usuário pedir para instalar algo (ex: instalar APOC, instalar pacote apt, etc)
        - Exemplo: instalar_aplicativo("apt install -y htop") ou instalar_aplicativo("wget ...", app_id="6729479663")

        ### `comandos_gerais(comando, diretorio=None, acao=None, arquivo=None, conteudo=None)`
        - **NOVA FUNÇÃO:** Executa comandos administrativos gerais não previstos nas funções acima
        - Use esta função para pausar, ativar, reiniciar serviços, listar arquivos, ler ou editar arquivos, ou qualquer outra operação administrativa
        - Sempre explique ao usuário o que será feito e confirme antes de executar

        ### `obter_credenciais_vps()`
        - Sempre que precisar das credenciais de administrador (host, usuário, senha, porta) do VPS, utilize esta função para obtê-las de forma segura e atualizada.
        - Nunca armazene as credenciais em variáveis fixas ou hardcoded; sempre chame esta função quando necessário.

        ## 📱 **Aplicativos Disponíveis:**
        {apps_info}

        ## 🚀 **Comandos Comuns que Aceito:**
        - "Tem algum deploy pendente?" → use `verificar_atualizacoes_github()`
        - "Há atualizações no GitHub?" → use `verificar_atualizacoes_github()`
        - "Faça deploy do Gptalk Server" → use `fazer_deploy_app()`
        - "Verifique o status do site" → use `verificar_status_app()` 
        - "Instale as dependências do backend" → use `instalar_dependencias_app()`
        - "Reinicie o serviço do servidor" → use `verificar_status_servico()`
        - "Mostre os logs do Gptalk App" → use `executar_comando_ssh()`
        - "Instale o APOC" → use `instalar_aplicativo()`

        ## ⚠️ **Diretrizes Importantes:**
        - **Sempre confirme** operações que modificam o sistema
        - **Explique** o que cada operação fará antes de executar
        - **Use nomes amigáveis** - posso identificar apps pelo nome além do ID
        - **Seja proativo** - sugira verificações após deployments
        - **Monitore erros** - sempre verifique se operações foram bem-sucedidas

        ## 👤 **Contexto da Sessão:**
        - Usuário: {usuario_nome}
        - Data/Hora: {data_hora}
        - Negócio: {negocio_idx}

        Responda sempre em português brasileiro e seja didático nas explicações.
        Sempre que executar uma operação importante, sugira próximos passos.
        """
    def get_instructions(self):
        """Retorna as instruções do agente conversacional ServerVps"""
        return self.instructions


def find_active_conversation(negocio_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{negocio_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


def get_conversa_key(negocio_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{negocio_idx}_{agente_nome}_{conversa_idx}"


# Função tool para instalar aplicativos/pacotes/extensões via SSH
@function_tool
async def instalar_aplicativo(comando_instalacao: str, app_id: str = None, diretorio: str = None):
    def get_instructions(self):
        return self.instructions
    """
    Executa um comando de instalação de pacote, extensão ou utilitário no servidor via SSH.
    Args:
        comando_instalacao (str): Comando de instalação (ex: apt install, wget, etc)
        app_id (str, opcional): ID do app para obter diretório base
        diretorio (str, opcional): Diretório onde executar o comando
    Retorna:
        Resultado da execução do comando
    """
    try:
        logger.info(f"=== 🛠️ instalar_aplicativo({comando_instalacao}, app_id={app_id}, diretorio={diretorio}) FUNCTION TOOL ===")
        resultado = agent.install_package(comando_instalacao, app_id, diretorio)
        if resultado.get("success"):
            logger.info(f"✅ Instalação executada com sucesso: {comando_instalacao}")
        else:
            logger.warning(f"⚠️ Instalação falhou: {resultado.get('message')}")
        return resultado
    except Exception as e:
        logger.error(f"❌ Erro ao instalar aplicativo: {e}")
        return {"success": False, "message": f"Erro ao instalar aplicativo: {str(e)}"}

    def get_instructions(self):
        return self.instructions


# ===============================================================================
# FUNÇÕES DE FERRAMENTAS (TOOLS) PARA O AGENTE SERVIDOR VPS
# ===============================================================================

@function_tool
async def listar_aplicativos():
    """
    Lista todos os aplicativos disponíveis no servidor VPS.
    
    Retorna informações sobre cada aplicativo incluindo:
    - ID único do aplicativo
    - Nome e descrição
    - Diretório no servidor
    - URL de acesso
    - Nome do serviço (se aplicável)
    
    Use esta função quando o usuário quiser saber quais apps estão disponíveis
    ou quando precisar identificar o ID de um app pelo nome.
    """
    try:
        logger.info("=== 📱 listar_aplicativos() FUNCTION TOOL ===")
        
        # Usar função existente para obter apps
        apps = agent.config.get_all_apps()
        
        # Formatar resposta amigável
        apps_list = []
        for app_id, app_info in apps.items():
            apps_list.append({
                "id": app_id,
                "nome": app_info["name"],
                "descricao": app_info["description"],
                "diretorio": app_info["root"],
                "url": app_info["url"],
                "servico": app_info.get("service_name", "N/A")
            })
        
        logger.info(f"✅ {len(apps_list)} aplicativos encontrados")
        
        return {
            "success": True,
            "aplicativos": apps_list,
            "total": len(apps_list),
            "message": f"Encontrados {len(apps_list)} aplicativos no servidor"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao listar aplicativos: {e}")
        return {
            "success": False,
            "message": f"Erro ao listar aplicativos: {str(e)}"
        }


@function_tool
async def verificar_status_app(app_id: str):
    """
    Verifica o status do repositório Git de um aplicativo.
    
    Args:
        app_id (str): ID do aplicativo (ex: "6729479663") ou nome (ex: "Gptalk Server")
    
    Retorna informações sobre:
    - Status do repositório Git (arquivos modificados, não commitados)
    - Commits recentes
    - Diretório e identificação do app
    
    Use quando quiser verificar se há mudanças pendentes no repositório
    ou ver o histórico recente de commits.
    """
    try:
        logger.info(f"=== 📊 verificar_status_app({app_id}) FUNCTION TOOL ===")
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        # Chamar função existente
        resultado = agent.get_app_status(app_id)
        
        if "error" in resultado:
            return {
                "success": False,
                "message": f"Erro ao verificar status: {resultado['error']}"
            }
        
        logger.info(f"✅ Status obtido para {resultado.get('app_name', app_id)}")
        
        return {
            "success": True,
            "dados": resultado,
            "message": f"Status do app {resultado.get('app_name', app_id)} obtido com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar status do app: {e}")
        return {
            "success": False,
            "message": f"Erro ao verificar status: {str(e)}"
        }





@function_tool
async def verificar_status_servico(app_id: str):
    """
    Verifica o status do serviço systemd de um aplicativo.
    
    Args:
        app_id (str): ID do aplicativo ou nome
    
    Retorna informações sobre:
    - Se o serviço está ativo/inativo
    - Se está habilitado para iniciar automaticamente
    - Logs recentes do serviço
    - Output completo do systemctl status
    
    Use para diagnosticar problemas de execução de serviços,
    verificar se aplicações estão rodando corretamente.
    """
    try:
        logger.info(f"=== 🔧 verificar_status_servico({app_id}) FUNCTION TOOL ===")
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        # Chamar função existente
        resultado = agent.get_service_status(app_id)
        
        if "error" in resultado:
            return {
                "success": False,
                "message": f"Erro ao verificar serviço: {resultado['error']}"
            }
        
        logger.info(f"✅ Status do serviço obtido para {resultado.get('app_name', app_id)}")
        
        return {
            "success": True,
            "dados": resultado,
            "message": f"Status do serviço {resultado.get('app_name', app_id)} obtido com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar status do serviço: {e}")
        return {
            "success": False,
            "message": f"Erro ao verificar serviço: {str(e)}"
        }


@function_tool
async def fazer_deploy_app(app_id: str):
    """
    Executa deploy completo de um aplicativo (git pull + restart se necessário).
    
    Args:
        app_id (str): ID do aplicativo ou nome
    
    Realiza as seguintes operações:
    1. Executa git pull do repositório
    2. Reinicia o serviço (se configurado)
    3. Verifica se operações foram bem-sucedidas
    
    Use quando quiser fazer deploy de uma nova versão da aplicação.
    Esta é a operação mais comum para atualizar aplicações no servidor.
    """
    try:
        logger.info(f"=== 🚀 fazer_deploy_app({app_id}) FUNCTION TOOL ===")
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        original_input = app_id
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        if not app_id.isdigit():
            return {
                "success": False,
                "message": f"Aplicativo '{original_input}' não encontrado. Use listar_aplicativos() para ver apps disponíveis."
            }
        
        # Chamar endpoint existente de update (que já faz git pull + restart)
        resultado = await update_app(app_id)
        
        logger.info(f"✅ Deploy executado para app {app_id}")
        
        return {
            "success": resultado.get("success", False),
            "dados": resultado,
            "message": f"Deploy do app {agent.config.get_app_name(app_id)} executado"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao fazer deploy: {e}")
        return {
            "success": False,
            "message": f"Erro ao fazer deploy: {str(e)}"
        }


@function_tool
async def instalar_dependencias_app(app_id: str):
    """
    Instala dependências Python de um aplicativo.
    
    Args:
        app_id (str): ID do aplicativo ou nome
    
    Operações realizadas:
    1. Ativa ambiente virtual Python
    2. Instala pacotes do requirements.txt ou paramiko
    3. Verifica se instalação foi bem-sucedida
    
    Use quando houver novos pacotes Python adicionados ao projeto
    ou quando encontrar erros de imports/módulos não encontrados.
    """
    try:
        logger.info(f"=== 📦 instalar_dependencias_app({app_id}) FUNCTION TOOL ===")
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        original_input = app_id
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        if not app_id.isdigit():
            return {
                "success": False,
                "message": f"Aplicativo '{original_input}' não encontrado."
            }
        
        # Chamar função existente
        resultado = agent.install_dependencies(app_id)
        
        logger.info(f"✅ Instalação de dependências executada para app {app_id}")
        
        return {
            "success": resultado.get("success", False),
            "dados": resultado,
            "message": f"Instalação de dependências do app {agent.config.get_app_name(app_id)} executada"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao instalar dependências: {e}")
        return {
            "success": False,
            "message": f"Erro ao instalar dependências: {str(e)}"
        }


@function_tool
async def verificar_informacoes_git(app_id: str):
    """
    Obtém informações detalhadas do repositório Git de um aplicativo.
    
    Args:
        app_id (str): ID do aplicativo ou nome
    
    Retorna informações sobre:
    - Branch atual e branches disponíveis
    - Remote origin configurado
    - Se há mudanças não commitadas
    - Se é um repositório Git válido
    
    Use para diagnosticar problemas de Git ou entender configuração do repositório.
    """
    try:
        logger.info(f"=== 🔍 verificar_informacoes_git({app_id}) FUNCTION TOOL ===")
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        original_input = app_id
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        if not app_id.isdigit():
            return {
                "success": False,
                "message": f"Aplicativo '{original_input}' não encontrado."
            }
        
        # Chamar função existente
        resultado = agent.get_git_info(app_id)
        
        if "error" in resultado:
            return {
                "success": False,
                "message": f"Erro ao obter informações Git: {resultado['error']}"
            }
        
        logger.info(f"✅ Informações Git obtidas para app {app_id}")
        
        return {
            "success": True,
            "dados": resultado,
            "message": f"Informações Git do app {resultado.get('app_name', app_id)} obtidas com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar informações Git: {e}")
        return {
            "success": False,
            "message": f"Erro ao verificar Git: {str(e)}"
        }


@function_tool
async def executar_comando_ssh(comando: str, diretorio: str = None):
    """
    Executa um comando personalizado via SSH no servidor.
    
    Args:
        comando (str): Comando a ser executado
        diretorio (str, optional): Diretório onde executar o comando
    
    ATENÇÃO: Esta função executa comandos diretamente no servidor.
    Use com cuidado e sempre explique o que o comando fará.
    
    Útil para:
    - Verificar logs específicos
    - Executar comandos de diagnóstico
    - Operações não cobertas pelas outras funções
    """
    try:
        logger.info(f"=== ⚡ executar_comando_ssh({comando}) FUNCTION TOOL ===")
        logger.info(f"Diretório: {diretorio}")
        
        # Chamar função SSH existente
        sucesso, stdout, stderr = agent.ssh_execute(comando, diretorio or "")
        
        resultado = {
            "comando": comando,
            "diretorio": diretorio,
            "sucesso": sucesso,
            "stdout": stdout,
            "stderr": stderr
        }
        
        if sucesso:
            logger.info(f"✅ Comando executado com sucesso")
            return {
                "success": True,
                "dados": resultado,
                "message": f"Comando '{comando}' executado com sucesso"
            }
        else:
            logger.warning(f"⚠️ Comando falhou: {stderr}")
            return {
                "success": False,
                "dados": resultado,
                "message": f"Comando '{comando}' falhou: {stderr}"
            }
        
    except Exception as e:
        logger.error(f"❌ Erro ao executar comando SSH: {e}")
        return {
            "success": False,
            "message": f"Erro ao executar comando: {str(e)}"
        }


@function_tool
async def verificar_atualizacoes_github(app_id: str):
    """
    Verifica se há atualizações no GitHub que ainda não foram aplicadas no servidor.
    
    Args:
        app_id (str): ID do aplicativo ou nome (ex: "6729479663" ou "Gptalk Server")
    
    Esta função faz git fetch e compara o repositório local com o remoto para detectar:
    - Commits novos no GitHub que ainda não foram baixados
    - Arquivos modificados localmente
    - Necessidade de deploy
    
    Use esta função quando quiser verificar se há atualizações pendentes do GitHub
    que precisam ser deployadas no servidor.
    
    IMPORTANTE: Se não especificar app_id, perguntará qual app verificar.
    """
    try:
        logger.info(f"=== 🔄 verificar_atualizacoes_github({app_id}) FUNCTION TOOL ===")
        
        # Se não foi especificado um app, sugerir usar listar_aplicativos primeiro
        if not app_id or app_id.lower() in ["todos", "all", "geral"]:
            return {
                "success": False,
                "message": "Por favor, especifique qual aplicativo verificar. Use listar_aplicativos() primeiro para ver as opções disponíveis."
            }
        
        # Se app_id não for numérico, tentar encontrar pelo nome
        original_input = app_id
        if not app_id.isdigit():
            for aid, app_info in appsOnserver.items():
                if app_info["name"].lower() == app_id.lower():
                    app_id = aid
                    break
        
        if not app_id.isdigit():
            return {
                "success": False,
                "message": f"Aplicativo '{original_input}' não encontrado. Apps disponíveis: Gptalk Server, Gptalk App Phj, Gptalk Site"
            }
        
        # Chamar função melhorada que verifica repositório remoto
        resultado = agent.check_remote_updates(app_id)
        
        if "error" in resultado:
            return {
                "success": False,
                "message": f"Erro ao verificar atualizações GitHub: {resultado['error']}"
            }
        
        logger.info(f"✅ Verificação de atualizações GitHub concluída para app {app_id}")
        
        # Formatação mais clara da resposta
        app_name = resultado.get('app_name', 'App desconhecido')
        needs_deploy = resultado.get('needs_deploy', False)
        status_summary = resultado.get('status_summary', 'Status não disponível')
        pending_count = resultado.get('pending_commits_count', 0)
        
        # Resposta detalhada
        response_message = f"Verificação de atualizações do {app_name}:\n\n"
        response_message += f"📊 Status: {status_summary}\n"
        
        if needs_deploy:
            response_message += f"🔄 DEPLOY NECESSÁRIO: {pending_count} commit(s) pendente(s)\n"
            if resultado.get('pending_commits'):
                response_message += "📝 Commits pendentes:\n"
                for commit in resultado['pending_commits'][:3]:  # Mostrar apenas os 3 mais recentes
                    response_message += f"   • {commit}\n"
        else:
            response_message += "✅ Nenhum deploy necessário no momento\n"
        
        if not resultado.get('fetch_success', True):
            response_message += "\n⚠️ Aviso: Git fetch falhou - dados podem estar desatualizados"
        
        return {
            "success": True,
            "dados": resultado,
            "needs_deploy": needs_deploy,
            "message": response_message.strip()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao verificar atualizações GitHub: {e}")
        return {
            "success": False,
            "message": f"Erro ao verificar atualizações GitHub: {str(e)}"
        }


@function_tool
async def testar_correcao_github(app_id: str = "6729479663"):
    """
    Função de teste para verificar se a correção do git fetch está funcionando.
    
    Args:
        app_id (str): ID do aplicativo a testar (padrão: Gptalk Server)
    
    Esta é uma função especial para testar se:
    - As credenciais GitHub estão configuradas
    - O git fetch está funcionando
    - A detecção de commits pendentes está correta
    
    Use para diagnosticar problemas de autenticação Git.
    """
    try:
        logger.info(f"=== 🧪 testar_correcao_github({app_id}) FUNCTION TOOL ===")
        
        # Verificar configurações básicas
        github_ok = agent.config.validate_github_credentials()
        vps_ok = agent.config.validate_vps_credentials()
        
        resultado_teste = f"🧪 Teste de Correção do Git Fetch\n\n"
        resultado_teste += f"📋 Configurações:\n"
        resultado_teste += f"   • Credenciais GitHub: {'✅ OK' if github_ok else '❌ Não configuradas'}\n"
        resultado_teste += f"   • Credenciais VPS: {'✅ OK' if vps_ok else '❌ Não configuradas'}\n\n"
        
        if not github_ok:
            return {
                "success": False,
                "message": resultado_teste + "❌ Credenciais GitHub não configuradas! Verifique as variáveis de ambiente."
            }
        
        if not vps_ok:
            return {
                "success": False,
                "message": resultado_teste + "❌ Credenciais VPS não configuradas! Verifique as variáveis de ambiente."
            }
        
        # Executar verificação
        resultado = agent.check_remote_updates(app_id)
        
        if "error" in resultado:
            return {
                "success": False,
                "message": resultado_teste + f"❌ Erro na verificação: {resultado['error']}"
            }
        
        # Analisar resultado
        fetch_ok = resultado.get('fetch_success', False)
        app_name = resultado.get('app_name', 'App desconhecido')
        
        resultado_teste += f"🔍 Teste do {app_name}:\n"
        resultado_teste += f"   • Git Fetch: {'✅ Sucesso' if fetch_ok else '❌ Falhou'}\n"
        
        if fetch_ok:
            needs_deploy = resultado.get('needs_deploy', False)
            pending_count = resultado.get('pending_commits_count', 0)
            resultado_teste += f"   • Deploy necessário: {'🔄 SIM' if needs_deploy else '✅ NÃO'}\n"
            resultado_teste += f"   • Commits pendentes: {pending_count}\n"
            resultado_teste += f"   • Status: {resultado.get('status_summary', 'N/A')}\n"
        else:
            resultado_teste += f"   • Erro fetch: {resultado.get('fetch_output', 'N/A')}\n"
        
        return {
            "success": True,
            "fetch_funcionou": fetch_ok,
            "dados": resultado,
            "message": resultado_teste.strip()
        }
        
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")
        return {
            "success": False,
            "message": f"❌ Erro no teste: {str(e)}"
        }


@function_tool
async def comandos_gerais(comando: str, diretorio: str = None, acao: str = None, arquivo: str = None, conteudo: str = None):
    """
    Executa comandos administrativos gerais no servidor VPS, incluindo:
    - Pausar, ativar, reiniciar serviços (systemctl)
    - Listar arquivos e diretórios (ls, tree)
    - Ler conteúdo de arquivos (cat, tail, head)
    - Editar ou sobrescrever arquivos (echo, redirecionamento, sed)
    - Outras operações administrativas não previstas nas funções específicas

    Todos os comandos são executados com 'sudo' para garantir permissões administrativas.

    Args:
        comando (str): Comando a ser executado (ex: systemctl stop nginx)
        diretorio (str, opcional): Diretório onde executar o comando
        acao (str, opcional): Ação administrativa (ex: 'listar', 'editar', 'parar_servico', etc)
        arquivo (str, opcional): Caminho do arquivo a ser manipulado
        conteudo (str, opcional): Conteúdo a ser escrito no arquivo (para edição)
    Retorna:
        Resultado da execução do comando
    """
    try:
        logger.info(f"=== ⚙️ comandos_gerais(comando={comando}, diretorio={diretorio}, acao={acao}, arquivo={arquivo}) FUNCTION TOOL ===")
        if not isinstance(diretorio, str):
            diretorio = ""
        # Obter senha do sudo
        vps_password = agent.config.vps_password if hasattr(agent.config, 'vps_password') else secret.get_secret("VPS_PASSWORD")
        def exec_ssh(cmd, diretorio):
            return agent.ssh_execute(cmd, diretorio or "")
        # Se for edição de arquivo
        if acao == 'editar' and arquivo and conteudo is not None:
            cmd = f"echo '{conteudo}' | echo '{vps_password}' | sudo -S tee {arquivo} > /dev/null"
            sucesso, stdout, stderr = exec_ssh(cmd, diretorio)
            return {
                "success": sucesso,
                "acao": acao,
                "arquivo": arquivo,
                "stdout": stdout,
                "stderr": stderr,
                "message": f"Arquivo {arquivo} editado com sucesso" if sucesso else f"Erro ao editar {arquivo}: {stderr}"
            }
        elif acao == 'ler' and arquivo:
            cmd = f"echo '{vps_password}' | sudo -S cat {arquivo}"
            sucesso, stdout, stderr = exec_ssh(cmd, diretorio)
            return {
                "success": sucesso,
                "acao": acao,
                "arquivo": arquivo,
                "stdout": stdout,
                "stderr": stderr,
                "message": f"Conteúdo de {arquivo} lido com sucesso" if sucesso else f"Erro ao ler {arquivo}: {stderr}"
            }
        elif acao == 'listar':
            cmd = comando or 'ls -l'
            if not cmd.strip().startswith('sudo'):
                cmd = f"echo '{vps_password}' | sudo -S {cmd}"
            sucesso, stdout, stderr = exec_ssh(cmd, diretorio)
            return {
                "success": sucesso,
                "acao": acao,
                "stdout": stdout,
                "stderr": stderr,
                "message": "Listagem executada com sucesso" if sucesso else f"Erro ao listar: {stderr}"
            }
        elif acao in ['parar_servico', 'iniciar_servico', 'reiniciar_servico'] and comando:
            cmd = comando
            if not cmd.strip().startswith('sudo'):
                cmd = f"echo '{vps_password}' | sudo -S {cmd}"
            sucesso, stdout, stderr = exec_ssh(cmd, diretorio)
            return {
                "success": sucesso,
                "acao": acao,
                "stdout": stdout,
                "stderr": stderr,
                "message": f"Ação {acao} executada" if sucesso else f"Erro ao executar ação {acao}: {stderr}"
            }
        else:
            cmd = comando
            if not cmd.strip().startswith('sudo'):
                cmd = f"echo '{vps_password}' | sudo -S {cmd}"
            sucesso, stdout, stderr = exec_ssh(cmd, diretorio)
            return {
                "success": sucesso,
                "acao": acao,
                "stdout": stdout,
                "stderr": stderr,
                "message": f"Comando '{comando}' executado" if sucesso else f"Erro ao executar comando: {stderr}"
            }
    except Exception as e:
        logger.error(f"❌ Erro em comandos_gerais: {e}")
        return {
            "success": False,
            "message": f"Erro ao executar comando geral: {str(e)}"
        }


@function_tool
async def obter_credenciais_vps():
    """
    Retorna as credenciais do VPS (host, port, user, password) para uso administrativo.
    Use esta função para depuração, diagnóstico ou para garantir que o agente tem acesso às credenciais corretas.
    """
    try:
        creds = agent.config.get_vps_credentials()
        return {
            "success": True,
            "credenciais": creds,
            "message": "Credenciais do VPS obtidas com sucesso."
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Erro ao obter credenciais do VPS: {str(e)}"
        }


# ==========================================
# ENDPOINTS API
# ==========================================

@router.post("/update-app/{app_id}")
async def update_app(app_id: str):
    """
    Endpoint para atualizar um app específico via git pull
    
    Args:
        app_id: ID do app a ser atualizado
        
    Returns:
        JSON com resultado da operação
    """
    try:
        logger.info(f"Recebida solicitação de atualização para app_id: {app_id}")
        
        # Executar git pull
        success, success_msg, error_msg = agent.git_pull_app(app_id)
        
        if success:
            # Executar comando de reinicialização do app (apenas para o Gptalk Server)
            restart_success = True
            restart_message = ""
            
            if app_id == "6729479663":  # Gptalk Server
                app_root = agent.config.get_app_root(app_id)
                # Usar -S para ler senha do stdin e echo para fornecer a senha
                restart_cmd = f"echo '{agent.config.vps_password}' | sudo -S systemctl restart gptalk-server"
                restart_success, restart_stdout, restart_stderr = agent.ssh_execute(restart_cmd, app_root)
                
                if restart_success:
                    restart_message = "Serviço reiniciado com sucesso"
                    logger.info(f"Restart executado com sucesso: {restart_stdout}")
                else:
                    restart_message = f"Erro ao reiniciar serviço: {restart_stderr}"
                    logger.warning(f"Aviso ao reiniciar o app: {restart_stderr}")

            return {
                "success": True,
                "message": success_msg,
                "app_id": app_id,
                "app_name": agent.config.get_app_name(app_id),
                "output": error_msg,  # stderr pode conter informações úteis mesmo em sucesso
                "restart_success": restart_success,
                "restart_message": restart_message
            }
        else:
            return {
                "success": False,
                "message": "Erro ao executar git pull",
                "error": error_msg,
                "app_id": app_id
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint update-app: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/app-status/{app_id}")
async def get_app_status(app_id: str):
    """
    Endpoint para obter status de um app específico
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com status do app
    """
    try:
        logger.info(f"Recebida solicitação de status para app_id: {app_id}")
        
        # Obter status do app
        status = agent.get_app_status(app_id)
        
        if "error" in status:
            return {
                "success": False,
                "message": "Erro ao obter status",
                "error": status["error"],
                "app_id": app_id
            }
        else:
            return {
                "success": True,
                "data": status
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint app-status: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/service-status/{app_id}")
async def get_service_status(app_id: str):
    """
    Endpoint para verificar status do serviço systemd de um app
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com status do serviço
    """
    try:
        logger.info(f"Recebida solicitação de status do serviço para app_id: {app_id}")
        
        # Obter status do serviço
        status = agent.get_service_status(app_id)
        
        if "error" in status:
            return {
                "success": False,
                "message": "Erro ao obter status do serviço",
                "error": status["error"],
                "app_id": app_id
            }
        else:
            return {
                "success": True,
                "data": status
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint service-status: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/apps")
async def list_apps():
    """
    Endpoint para listar todos os apps disponíveis
    
    Returns:
        JSON com lista de apps
    """
    try:
        logger.info("Recebida solicitação de listagem de apps")
        
        apps = agent.config.get_all_apps()
        
        # Formatar resposta
        apps_list = []
        for app_id, app_info in apps.items():
            apps_list.append({
                "id": app_id,
                "name": app_info["name"],
                "description": app_info["description"],
                "root": app_info["root"],
                "url": app_info["url"]
            })
        
        return {
            "success": True,
            "message": f"Encontrados {len(apps_list)} apps",
            "data": apps_list
        }
        
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint apps: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.post("/install-dependencies/{app_id}")
async def install_dependencies(app_id: str):
    """
    Endpoint para instalar dependências Python de um app
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com resultado da instalação
    """
    try:
        logger.info(f"Recebida solicitação de instalação de dependências para app_id: {app_id}")
        
        # Instalar dependências
        result = agent.install_dependencies(app_id)
        
        if result.get("success"):
            return {
                "success": True,
                "message": result["message"],
                "app_id": app_id,
                "app_name": agent.config.get_app_name(app_id),
                "output": result.get("output", "")
            }
        else:
            return {
                "success": False,
                "message": result.get("message", "Erro ao instalar dependências"),
                "error": result.get("error", "Erro desconhecido"),
                "app_id": app_id
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint install-dependencies: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/git-info/{app_id}")
async def get_git_info(app_id: str):
    """
    Endpoint para obter informações detalhadas do Git de um app
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com informações do Git
    """
    logger.info("===== get_git_info() =====")
    logger.info(f"app_id: {app_id}")
    
    try:
        logger.info(f"Recebida solicitação de informações Git para app_id: {app_id}")
        
        # Obter informações do Git
        git_info = agent.get_git_info(app_id)
        logger.info(f"git_info: {git_info}")
        
        if "error" in git_info:
            return {
                "success": False,
                "message": "Erro ao obter informações do Git",
                "error": git_info["error"],
                "app_id": app_id
            }
        else:
            return {
                "success": True,
                "data": git_info
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint git-info: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/check-github-updates/{app_id}")
async def check_github_updates(app_id: str):
    """
    Endpoint para verificar se há atualizações no GitHub que ainda não foram aplicadas no servidor
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com informações sobre atualizações pendentes do GitHub
    """
    logger.info("===== check_github_updates() =====")
    logger.info(f"app_id: {app_id}")
    
    try:
        logger.info(f"Recebida solicitação de verificação de atualizações GitHub para app_id: {app_id}")
        
        # Verificar atualizações do GitHub
        github_updates = agent.check_remote_updates(app_id)
        logger.info(f"github_updates: {github_updates}")
        
        if "error" in github_updates:
            return {
                "success": False,
                "message": "Erro ao verificar atualizações do GitHub",
                "error": github_updates["error"],
                "app_id": app_id
            }
        else:
            return {
                "success": True,
                "data": github_updates,
                "needs_deploy": github_updates.get("needs_deploy", False),
                "summary": github_updates.get("status_summary", "Status não disponível")
            }
            
    except Exception as e:
        logger.error(f"Erro inesperado no endpoint check-github-updates: {str(e)}")
        return {
            "success": False,
            "message": "Erro interno do servidor",
            "error": str(e)
        }


@router.get("/test-github-fetch/{app_id}")
async def test_github_fetch(app_id: str):
    """
    Endpoint de teste específico para verificar se o git fetch com credenciais está funcionando
    
    Args:
        app_id: ID do app
        
    Returns:
        JSON com resultado detalhado do teste
    """
    logger.info("===== test_github_fetch() - ENDPOINT DE TESTE =====")
    logger.info(f"app_id: {app_id}")
    
    try:
        # Verificar credenciais GitHub
        github_ok = agent.config.validate_github_credentials()
        vps_ok = agent.config.validate_vps_credentials()
        
        # Verificar atualizações
        github_updates = agent.check_remote_updates(app_id)
        
        return {
            "success": True,
            "teste_info": {
                "github_credentials_ok": github_ok,
                "vps_credentials_ok": vps_ok,
                "app_id": app_id,
                "app_name": agent.config.get_app_name(app_id)
            },
            "resultado_verificacao": github_updates,
            "resumo": {
                "fetch_funcionou": github_updates.get("fetch_success", False),
                "precisa_deploy": github_updates.get("needs_deploy", False),
                "commits_pendentes": github_updates.get("pending_commits_count", 0),
                "status": github_updates.get("status_summary", "N/A")
            }
        }
        
    except Exception as e:
        logger.error(f"Erro no teste: {str(e)}")
        return {
            "success": False,
            "message": "Erro no teste",
            "error": str(e)
        }


@router.get("/health")
async def health_check():
    """
    Endpoint para verificar saúde do serviço
    
    Returns:
        JSON com status do serviço
    """
    try:
        # Verificar configurações
        github_ok = agent.config.validate_github_credentials()
        vps_ok = agent.config.validate_vps_credentials()
        
        return {
            "success": True,
            "service": "Agent Server VPS",
            "status": "online",
            "github_configured": github_ok,
            "vps_configured": vps_ok,
            "apps_count": len(agent.config.get_all_apps())
        }
        
    except Exception as e:
        logger.error(f"Erro no health check: {str(e)}")
        return {
            "success": False,
            "service": "Agent Server VPS",
            "status": "error",
            "error": str(e)
        }


# ===============================================================================
# ENDPOINTS CONVERSACIONAIS DO AGENTE SERVIDOR VPS
# ===============================================================================

@router.post("/send/text")
async def send_text_servervps(data: dict):
    """
    Endpoint principal para processar mensagens de texto para o agente ServerVPS
    Converte comandos em linguagem natural para operações no servidor
    """
    logger.info("===== send_text_servervps() =====")
    logger.info(f"data: {data}")
    
    try:
        # Extrair dados necessários
        mensagem = data.get("mensagem", "")
        modelo = data.get("modelo", "")
        mensagem = data.get("mensagem") or ""
        modelo = data.get("modelo") or "**********"
        usuario_nome = data.get("usuario_nome") or "ServerVPS User"
        negocio_idx = data.get("negocio_idx") or ""
        usuario_idx = data.get("usuario_idx") or ""
        usuario_nome = data.get("usuario_nome", "ServerVPS User")
        negocio_idx = data.get("negocio_idx", "")
        usuario_idx = data.get("usuario_idx", "")
        
        if not mensagem:
            return {"success": False, "message": "Mensagem é obrigatória"}
        
        if not modelo:
            return {"success": False, "message": "Modelo é obrigatório"}

        # Linha 2152-2154
        if usuario_idx != "1122334455":
            return {"success": False, "message": "Você não tem permissão para usar este agente"}


        
        # Verificar se existe conversa ativa
        conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "server-vps")
        
        if not conversa_idx:
            conversa_idx = generate_unique_id()
            historico_mensagens = []
        
        # Adicionar mensagem do usuário ao histórico
        historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)
        
        # Criar agente ServerVPS
        agentServerVps = AgentServerVpsConversacional(
            negocio_idx=negocio_idx,
            usuario_nome=usuario_nome
        )
        
        # Carregar modelo
        llm = LLM()
        model = llm.get_model_idx(modelo)
        logger.info(f"Modelo carregado: {model}")
        
        instructions = agentServerVps.get_instructions()
        
        # Configurar agente
        agente_config = {
            "name": "Server VPS Management Agent",
            "instructions": instructions,
            "model": model,
            "tools": [
                listar_aplicativos,
                verificar_atualizacoes_github,
                testar_correcao_github,
                verificar_status_app,
                verificar_status_servico,
                fazer_deploy_app,
                instalar_dependencias_app,
                verificar_informacoes_git,
                executar_comando_ssh,
                comandos_gerais,
                obter_credenciais_vps,
            ],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }
        
        # Criar o agente
        agente_obj = await oai.agent_create(**agente_config)
        logger.info("Agente ServerVPS criado com sucesso")
        
        # Processar com o agente e obter resposta única (igual Neo4j)
        resposta_completa = ""
        try:
            async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                if isinstance(chunk, bytes):
                    chunk_str = chunk.decode('utf-8')
                else:
                    chunk_str = str(chunk)
                resposta_completa += chunk_str
            # Adicionar resposta ao histórico
            historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
            # Salvar histórico no cache
            cache_key = get_conversa_key(negocio_idx, "server-vps", conversa_idx)
            cache[cache_key] = historico_mensagens
            # Tenta decodificar resposta_completa como JSON
            import json
            try:
                resposta_json = json.loads(resposta_completa)
                if isinstance(resposta_json, dict) and 'status' in resposta_json and 'messages' in resposta_json:
                    return resposta_json
            except Exception:
                pass
            logger.info("Resposta completa: %s", resposta_completa)
            return {"success": True, "message": resposta_completa}
        except Exception as e:
            logger.error(f"Erro durante execução sem streaming: {str(e)}")
            return {"success": False, "message": f"Erro durante execução sem streaming: {str(e)}"}
        
    except Exception as e:
        logger.error(f"Erro no endpoint send_text_servervps: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


@router.post("/drop/messages")
async def drop_messages_servervps(data: dict):
    """
    Endpoint para limpar cache de conversas do agente ServerVPS
    
    Parâmetros:
    - negocio_idx (str): ID do negócio (obrigatório)
    """
    logger.info("===== 🗑️🚮 ServerVPS drop_messages() 🗑️🚮 =====")
    logger.info(f"data: {data}")
    
    negocio_idx = data.get("negocio_idx", "")
    
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    try:
        chaves_removidas = 0
        chaves_para_remover = []
        
        # Buscar chaves do cache que correspondem ao agente server-vps
        for key in list(cache.keys()):
            # Filtrar por conversas do agente server-vps
            if key.startswith(f"conversa_{negocio_idx}_server-vps_"):
                chaves_para_remover.append(key)
        
        # Remover as chaves identificadas do cache
        for key in chaves_para_remover:
            try:
                del cache[key]
                chaves_removidas += 1
                logger.info(f"Chave removida do cache: {key}")
            except KeyError:
                logger.warning(f"Chave já não existe no cache: {key}")
        
        logger.info(f"Cache limpo: {chaves_removidas} conversas removidas")
        
        return {
            "success": True,
            "message": f"Cache de conversas ServerVPS limpo com sucesso! {chaves_removidas} conversas removidas.",
            "detalhes": {
                "conversas_removidas": chaves_removidas,
                "filtro_usado": f"negocio_idx: {negocio_idx}"
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {str(e)}")
        return {
            "success": False,
            "message": f"Erro ao limpar cache: {str(e)}"
        }


#=========================================== 

if __name__ == "__main__":
    import asyncio

    async def gptalk_app_phj():

        resposta_update_app = await update_app("8387520375")
        logger.info("update_app: %s", resposta_update_app)


    async def gptalk_server():
        resposta_lista_apps = await  list_apps()
        logger.info("apps: %s", resposta_lista_apps)

        resposta_status_app = await get_app_status("6729479663")
        logger.info("status_app: %s", resposta_status_app)

        resposta_service_status = await get_service_status("6729479663")
        logger.info("service_status: %s", resposta_service_status)

        # Instalar dependências
        resposta_install_deps = await install_dependencies("6729479663")
        logger.info("install_dependencies: %s", resposta_install_deps)

        # Reiniciar o serviço após instalação das dependências
        if resposta_install_deps.get("success"):
            resposta_restart = await update_app("6729479663")
            logger.info("restart_after_install: %s", resposta_restart)

        return;

    async def gptalk():
        # Verificar informações Git antes do update
        resposta_git_info = await get_git_info("4961585763")
        logger.info("git_info: %s", resposta_git_info)

        resposta_update_app = await update_app("4961585763")
        logger.info("update_app: %s", resposta_update_app)

    async def test_github_updates():
        """Teste da nova funcionalidade de verificar atualizações do GitHub"""
        logger.info("=== TESTANDO FUNCIONALIDADE CORRIGIDA check_github_updates ===")
        
        # Testar apenas Gptalk Server (o que o usuário está mais interessado)
        resposta_github_updates = await check_github_updates("6729479663")
        logger.info("github_updates Gptalk Server: %s", resposta_github_updates)
        
        # Testar diretamente a função melhorada
        resultado_direto = agent.check_remote_updates("6729479663")
        logger.info("resultado direto check_remote_updates: %s", resultado_direto)
        
        # Verificar se a configuração de credenciais está funcionando
        if "fetch_success" in resultado_direto:
            logger.info(f"Git fetch success: {resultado_direto['fetch_success']}")
            logger.info(f"Status summary: {resultado_direto.get('status_summary', 'N/A')}")
            logger.info(f"Needs deploy: {resultado_direto.get('needs_deploy', 'N/A')}")

    async def teste_envia_texto():
        """Teste do endpoint send_text_servervps"""
        logger.info("=== TESTANDO ENDPOINT send_text_servervps ===")
        
        # Preparar dados de teste
        data = {
            'mensagem': 'Olá, qual é o seu nome?',
            'modelo': '',
            'usuario_idx': '1122334455',
            'usuario_nome': 'Carlos Silva',
            'negocio_idx': '1234567890'
        }
        
        # Chamar o endpoint
        resposta = await send_text_servervps(data)
        # Linha 1999-2000
        logger.info("Resposta do endpoint: %s", json.dumps(resposta, ensure_ascii=False, indent=2))



        
    #asyncio.run(gptalk_server())
    #asyncio.run(gptalk())
    #asyncio.run(gptalk_app_phj())
    asyncio.run(teste_envia_texto())
