from .agent_logger import <PERSON><PERSON>ogger
from fastapi import APIRouter
from .agent_mysql import Mysql


logger = AgentLogger()
router = APIRouter()


class AgentWorkOrder:
    def __init__(self):
        self.nome = "WorkOrder"
        self.descricao = "Agente de Ordens de Serviço"
        self.tipo = "workorder"
        self.mysql = Mysql()

    async def get_proximo_numero_ordem(self, negocio_idx: str) -> int:
        """
        Obtém o próximo número sequencial para ordem de serviço do negócio
        Thread-safe usando ON DUPLICATE KEY UPDATE
        
        Args:
            negocio_idx (str): IDX do negócio
            
        Returns:
            int: Próximo número da ordem de serviço para este negócio
            
        Raises:
            Exception: Se houver erro na operação
        """
        try:
            logger.info(f"🔢 Obtendo próximo número da ordem para negócio: {negocio_idx}")
            
            # Query atomica que incrementa e retorna o próximo número
            # ON DUPLICATE KEY UPDATE garante thread-safety
            query_insert = """
                INSERT INTO NEGOCIO_ORDEM_SERVICO_NUMERACAO (NEGOCIO_IDX, ULTIMO_NUMERO) 
                VALUES (%s, 1) 
                ON DUPLICATE KEY UPDATE ULTIMO_NUMERO = ULTIMO_NUMERO + 1
            """
            
            # Executar o INSERT/UPDATE atômico
            await self.mysql.query(query_insert, params=[negocio_idx])
            logger.info(f"✅ Numeração atualizada para negócio {negocio_idx}")
            
            # Buscar o número atual
            query_select = """
                SELECT ULTIMO_NUMERO 
                FROM NEGOCIO_ORDEM_SERVICO_NUMERACAO 
                WHERE NEGOCIO_IDX = %s
            """
            resultado = await self.mysql.query(query_select, params=[negocio_idx])
            
            if not resultado or len(resultado) == 0:
                raise Exception("Erro ao obter número da ordem após inserção")
            
            proximo_numero = resultado[0]['ULTIMO_NUMERO']
            logger.info(f"🎯 Próximo número da ordem para negócio {negocio_idx}: {proximo_numero}")
            
            return proximo_numero
            
        except Exception as e:
            logger.error(f"❌ Erro ao obter próximo número da ordem: {e}")
            logger.error(f"❌ Negócio IDX: {negocio_idx}")
            
            # Log mais detalhado do erro
            import traceback
            logger.error(f"❌ Stack trace: {traceback.format_exc()}")
            
            raise Exception(f"Erro ao gerar número da ordem de serviço: {str(e)}")

    def _format_query_with_params(self, sql: str, params: tuple) -> str:
        """Formata a query SQL substituindo os placeholders pelos valores reais"""
        try:
            # Substituir %s pelos valores reais
            formatted_sql = sql
            for param in params:
                if isinstance(param, str):
                    # Escapar aspas simples e adicionar aspas
                    escaped_param = param.replace("'", "''")
                    formatted_sql = formatted_sql.replace("%s", f"'{escaped_param}'", 1)
                else:
                    # Para números, booleanos, etc.
                    formatted_sql = formatted_sql.replace("%s", str(param), 1)
            
            return formatted_sql
        except Exception as e:
            logger.error(f"Erro ao formatar query: {e}")
            return f"ERRO AO FORMATAR: {sql} | PARAMS: {params}"

    def _construir_busca_inteligente(self, texto_busca: str, negocio_idx: str):
        """
        Constrói busca inteligente por múltiplas palavras nos campos OS_NUMERO (formatado), CLIENTE_NOME, ITEM_NOME
        
        Args:
            texto_busca (str): Texto digitado pelo usuário (ex: "joão motor" ou "0003") 
            negocio_idx (str): IDX do negócio
            
        Returns:
            tuple: (where_clause, params_list)
            
        Exemplos:
            "joão motor" → Busca ordens que contenham ambas as palavras
            "0003" → Busca por número da ordem formatado (ex: 3 vira 0003)
            "maria" → Busca por nome do cliente
        """
        logger.info(f"🔍 Construindo busca inteligente para: '{texto_busca}'")
        
        # Separar o texto em palavras individuais
        palavras = [palavra.strip() for palavra in texto_busca.split() if palavra.strip()]
        
        if not palavras:
            # Se não há palavras válidas, retornar busca básica sem registros excluídos
            return "WHERE OS_NEGOCIO_IDX = %s AND EXCLUIDO = 0", [negocio_idx]
        
        if len(palavras) == 1:
            # Busca simples com uma palavra em OS_NUMERO (formatado com zeros), CLIENTE_NOME, ITEM_NOME
            palavra = palavras[0]
            where_clause = "WHERE (LPAD(OS_NUMERO, 4, '0') LIKE %s OR CLIENTE_NOME LIKE %s OR ITEM_NOME LIKE %s) AND OS_NEGOCIO_IDX = %s AND EXCLUIDO = 0"
            like_param = f'%{palavra}%'
            params = [like_param, like_param, like_param, negocio_idx]
            logger.info(f"🔍 Busca simples (1 palavra): '{palavra}'")
            return where_clause, params
        
        # Busca com múltiplas palavras
        logger.info(f"🔍 Busca inteligente com {len(palavras)} palavras: {palavras}")
        
        # Busca com LIKE para múltiplas palavras
        # Cada palavra deve estar presente em pelo menos uma das colunas
        conditions = []
        params = []
        
        for palavra in palavras:
            like_param = f'%{palavra}%'
            condition = "(LPAD(OS_NUMERO, 4, '0') LIKE %s OR CLIENTE_NOME LIKE %s OR ITEM_NOME LIKE %s)"
            conditions.append(condition)
            # Adicionar o mesmo parâmetro 3 vezes (uma para cada coluna)
            params.extend([like_param, like_param, like_param])
        
        # Juntar todas as condições com AND (todas as palavras devem estar presentes)
        where_clause = "WHERE " + " AND ".join(conditions) + " AND OS_NEGOCIO_IDX = %s AND EXCLUIDO = 0"
        params.append(negocio_idx)
        
        logger.info(f"✅ Usando LIKE com {len(palavras)} palavras")
        logger.info(f"🔍 Condições: {len(conditions)} grupos de condições")
        
        return where_clause, params

    async def search_all_workorders(self, filters: dict):
        """
        Busca ordens de serviço no Neo4j com filtros, paginação e busca inteligente,
        carregando o status e prioridade ativos ou, como fallback, os valores iniciais.
        """
        from .agent_neo4j import AgentNeo4j
        from .agent_logger import AgentLogger
        from datetime import datetime
        logger = AgentLogger()
        try:
            logger.info(f"===== search_all_workorders() (Neo4j) ===== {filters}")
            colunas = filters.get("colunas_nome", [
                "OS_IDX", "OS_NUMERO", "CLIENTE_NOME", "ITEM_NOME", "TOTAL", "ENTRADA", "SAIDA", "STATUS", "PRIORIDADE"
            ])
            texto_busca = filters.get("text", "")
            offset = filters.get('offset', 0)
            limit = filters.get('limit', 10)
            negocio_idx = filters.get('negocio_idx', '')
            if not negocio_idx:
                return {
                    "success": False, "data": [], "total": 0, "message": "negocio_idx é obrigatório"
                }

            # Mapas para fallback (valor inicial numérico para nome)
            status_map = {
                0: "A Iniciar", 1: "Em Andamento", 2: "Aguardando Peças",
                3: "Aguardando Cliente", 4: "Concluída", 5: "Cancelada", 6: "Entregue"
            }
            prioridade_map = {
                0: "Normal", 1: "Baixa", 2: "Média", 3: "Alta", 4: "Urgente", 5: "Crítica"
            }

            neo4j = getattr(self, 'neo4j', None)
            if neo4j is None:
                neo4j = AgentNeo4j()
            
            params = {"negocio_idx": negocio_idx}
            cypher = """
            MATCH (os:OrdemServico)-[:EMITIDA_POR]->(n:Negocio {idx: $negocio_idx})
            WHERE os.excluido = 0
            OPTIONAL MATCH (os)-[:SOLICITADA_POR]->(c:Pessoa)
            // Carrega o status ATIVO, se houver
            OPTIONAL MATCH (os)-[:TEM_STATUS {ativo: true}]->(st:Status)
            // Carrega a prioridade ATIVA, se houver
            OPTIONAL MATCH (os)-[:TEM_PRIORIDADE {ativo: true}]->(p:Prioridade)
            """
            
            if texto_busca and texto_busca.strip():
                cypher += " AND (toString(os.numero) CONTAINS $texto_busca OR toLower(os.item_nome) CONTAINS toLower($texto_busca) OR toLower(c.nome) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
            
            cypher += " RETURN os, c, st, p ORDER BY os.entrada DESC, os.idx DESC"
            
            if limit > 0:
                cypher += " SKIP $offset LIMIT $limit"
                params["offset"] = offset
                params["limit"] = limit
            
            logger.info(f"🔍 Cypher FINAL: {cypher}")
            logger.info(f"🔍 Params: {params}")
            
            result = await neo4j.execute_read_query(cypher, params)
            dados = []
            
            for r in result:
                os_node = r.get("os", {})
                if not os_node:
                    continue

                cliente = r.get("c", {})
                status_node = r.get("st")
                prioridade_node = r.get("p")
                
                logger.info(f"🔍 Processando OS: {os_node.get('numero')}")

                # Determina o nome do Status
                status_nome = None
                if status_node:
                    status_nome = status_node.get("nome")
                else:
                    # Fallback para o status inicial no nó da OS
                    status_id = os_node.get("status")
                    if status_id is not None:
                        status_nome = status_map.get(status_id)

                # Determina o nome da Prioridade
                prioridade_nome = None
                if prioridade_node:
                    prioridade_nome = prioridade_node.get("nome")
                else:
                    # Fallback para a prioridade inicial no nó da OS
                    prioridade_id = os_node.get("prioridade")
                    if prioridade_id is not None:
                        prioridade_nome = prioridade_map.get(prioridade_id)

                entrada_dt = os_node.get("entrada")
                saida_dt = os_node.get("saida")

                # Monta a linha com os dados necessários
                linha = {
                    "OS_IDX": os_node.get("idx"),
                    "OS_NUMERO": os_node.get("numero"),
                    "CLIENTE_NOME": cliente.get("nome") if cliente else None,
                    "ITEM_NOME": os_node.get("item_nome"),
                    "TOTAL": os_node.get("total"),
                    "ENTRADA": entrada_dt.to_native().strftime("%Y-%m-%d %H:%M:%S") if entrada_dt else None,
                    "SAIDA": saida_dt.to_native().strftime("%Y-%m-%d %H:%M:%S") if saida_dt else None,
                    "STATUS": status_nome,
                    "PRIORIDADE": prioridade_nome,
                }
                
                # Adiciona colunas extras se solicitadas
                for coluna in colunas:
                    if coluna not in linha:
                        linha[coluna] = os_node.get(coluna.lower())
                
                dados.append({k: linha.get(k) for k in colunas})
            
            resposta = {
                "success": True,
                "data": dados,
                "total": len(dados),
                "message": f"Encontradas {len(dados)} ordens de serviço" + (f" para '{texto_busca}'" if texto_busca else "")
            }
            logger.info(f"Resposta construída com sucesso: {len(dados)} ordens de serviço")
            return resposta
            
        except Exception as e:
            logger.error(f"❌ ERRO em search_all_workorders: {str(e)}")
            # O bloco de tratamento de erro foi simplificado para evitar erros de referência.
            error_msg = str(e).lower()
            if "doesn't exist" in error_msg or "não existe" in error_msg:
                logger.error(f"🔥 Label/Nó NÃO EXISTE: {e}")
                return {
                    "success": False, "error": "Label ou nó não existe no banco de dados",
                    "message": "Erro de configuração - verifique o schema do Neo4j",
                    "data": [], "total": 0
                }
            elif "unknown column" in error_msg or "coluna desconhecida" in error_msg:
                logger.error(f"🔥 Propriedade NÃO EXISTE: Verifique as propriedades {colunas}")
                return {
                    "success": False, "error": f"Uma ou mais propriedades não existem: {colunas}",
                    "message": "Erro de estrutura do banco - verifique as propriedades dos nós",
                    "data": [], "total": 0
                }
            else:
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {
                    "success": False, "error": str(e), "message": "Erro interno do sistema",
                    "data": [], "total": 0
                }
        finally:
            logger.info("procura finalizada")

    async def update_workorder(self, data: dict):
        """
        Atualiza uma ordem de serviço (status, prioridade, etc.) no Neo4j,
        mantendo um histórico de alterações de status/prioridade no Neo4j.
        """
        logger.info(f"🔄 Atualizando ordem de serviço com histórico: {data}")
        
        try:
            # 1. Extrair e validar parâmetros
            os_numero = data.get('os_numero')
            negocio_idx = data.get('negocio_idx')
            campo = data.get('campo', '').upper()
            valor = data.get('valor')
            
            if not all([os_numero, negocio_idx, campo, valor is not None]):
                raise ValueError("Parâmetros os_numero, negocio_idx, campo e valor são obrigatórios")

            os_numero = int(os_numero)
            valor = int(valor)

            campos_permitidos = ['STATUS', 'PRIORIDADE']
            if campo not in campos_permitidos:
                raise ValueError(f"Campo '{campo}' não é permitido. Válidos: {campos_permitidos}")

            status_map = {
                0: "A Iniciar", 1: "Em Andamento", 2: "Aguardando Peças",
                3: "Aguardando Cliente", 4: "Concluída", 5: "Cancelada", 6: "Entregue"
            }
            prioridade_map = {
                0: "Normal", 1: "Baixa", 2: "Média", 3: "Alta", 4: "Urgente", 5: "Crítica"
            }

            if campo == 'STATUS' and valor not in status_map:
                raise ValueError(f"Status {valor} inválido. Válidos: {list(status_map.keys())}")
            if campo == 'PRIORIDADE' and valor not in prioridade_map:
                raise ValueError(f"Prioridade {valor} inválida. Válidas: {list(prioridade_map.keys())}")

            logger.info(f"📝 Atualizando OS {os_numero}: {campo} = {valor}")

            # 2. Lógica de atualização do MySQL (mantida para compatibilidade)
            status_anterior = None
            if campo == 'STATUS':
                query_status_atual = "SELECT STATUS FROM NEGOCIO_ORDEM_SERVICO WHERE NUMERO = %s AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
                resultado_status = await self.mysql.query(query_status_atual, params=[os_numero, negocio_idx])
                if resultado_status:
                    status_anterior = resultado_status[0].get('STATUS')

            query_mysql = f"UPDATE NEGOCIO_ORDEM_SERVICO SET {campo} = %s WHERE NUMERO = %s AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
            params_mysql = [valor, os_numero, negocio_idx]
            if campo == 'STATUS':
                if valor == 6:  # Entregue
                    query_mysql = f"UPDATE NEGOCIO_ORDEM_SERVICO SET {campo} = %s, SAIDA = NOW() WHERE NUMERO = %s AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
                elif status_anterior == 6 and valor != 6:
                    query_mysql = f"UPDATE NEGOCIO_ORDEM_SERVICO SET {campo} = %s, SAIDA = NULL WHERE NUMERO = %s AND NEGOCIO_IDX = %s AND EXCLUIDO = 0"
            
            result_mysql = await self.mysql.query(query_mysql, params=params_mysql)
            if not hasattr(result_mysql, 'rowcount') or result_mysql.rowcount == 0:
                logger.warning(f"⚠️ Nenhuma linha afetada no MySQL para OS {os_numero}.")
                # Não retorna erro aqui para permitir que o Neo4j seja atualizado mesmo assim

            logger.info(f"✅ (MySQL) Ordem de serviço {os_numero} atualizada.")
            
            # 3. Lógica de atualização do Neo4j com histórico
            neo4j_message = ""
            try:
                from .agent_neo4j import AgentNeo4j
                neo4j = AgentNeo4j()

                target_label = 'Status' if campo == 'STATUS' else 'Prioridade'
                relationship_type = 'TEM_STATUS' if campo == 'STATUS' else 'TEM_PRIORIDADE'
                node_name_map = status_map if campo == 'STATUS' else prioridade_map
                target_node_name = node_name_map.get(valor)

                cypher_params = {
                    "negocio_idx": negocio_idx,
                    "os_numero": os_numero,
                    "target_node_name": target_node_name
                }

                set_saida_clause = ""
                if campo == 'STATUS':
                    if valor == 6: # Entregue
                        set_saida_clause = "SET os.saida = datetime()"
                    elif status_anterior == 6 and valor != 6:
                        set_saida_clause = "SET os.saida = NULL"

                cypher_query = f"""
                    MATCH (os:OrdemServico)-[:EMITIDA_POR]->(:Negocio {{idx: $negocio_idx}})
                    WHERE os.numero = $os_numero
                    MATCH (target_node:{target_label} {{nome: $target_node_name}})

                    // Desativa o relacionamento ativo anterior
                    OPTIONAL MATCH (os)-[old_rel:{relationship_type}]->() WHERE old_rel.ativo = true
                    SET old_rel.ativo = false

                    // Cria o novo relacionamento ativo com data_hora
                    CREATE (os)-[new_rel:{relationship_type}]->(target_node)
                    SET new_rel.data_hora = datetime(), new_rel.ativo = true
                    
                    // Atualiza a data de saída na OS, se aplicável
                    {set_saida_clause}
                """
                
                logger.info(f"🚀 Executando atualização com histórico no Neo4j para OS {os_numero}")
                await neo4j.execute_write_query(cypher_query, cypher_params)
                logger.info(f"✅ (Neo4j) Histórico da Ordem de Serviço {os_numero} atualizado.")
            
            except Exception as neo4j_error:
                logger.error(f"❌ Erro durante a atualização no Neo4j para OS {os_numero}: {neo4j_error}")
                neo4j_message = f" (Atenção: falha ao sincronizar com Neo4j: {neo4j_error})"

            # 4. Retorno
            return {
                "success": True,
                "message": f"Ordem de serviço {os_numero} atualizada com sucesso.{neo4j_message}",
                "data": {
                    "os_numero": os_numero,
                    "campo": campo,
                    "valor": valor,
                    "negocio_idx": negocio_idx
                }
            }

        except ValueError as e:
            logger.error(f"❌ Erro de validação: {e}")
            return {"success": False, "error": "Erro de validação", "message": str(e), "data": {}}
        except Exception as e:
            logger.error(f"❌ Erro ao atualizar ordem de serviço: {e}")
            import traceback
            logger.error(f"❌ Stack trace: {traceback.format_exc()}")
            return {"success": False, "error": "Erro interno", "message": f"Erro ao atualizar ordem de serviço: {str(e)}", "data": {}}

    async def delete_workorder(self, data: dict):
        """
        Deleta uma ordem de serviço (soft delete) no Neo4j.
        """
        logger.info(f"🗑️ Deletando ordem de serviço (Neo4j): {data}")
        
        try:
            os_numero = int(data['os_numero'])
            negocio_idx = data['negocio_idx']
        except (ValueError, TypeError) as e:
            logger.error(f"❌ Erro na conversão dos parâmetros: {e}")
            return {"success": False, "message": "Parâmetros os_numero e negocio_idx são inválidos.", "data": {}}

        try:
            from .agent_neo4j import AgentNeo4j
            neo4j = AgentNeo4j()
            
            cypher_query = """
                MATCH (os:OrdemServico)-[:EMITIDA_POR]->(:Negocio {idx: $negocio_idx})
                WHERE os.numero = $os_numero
                SET os.excluido = 1
            """
            params = {"negocio_idx": negocio_idx, "os_numero": os_numero}
            
            await neo4j.execute_write_query(cypher_query, params)
            logger.info(f"✅ (Neo4j) Ordem de serviço {os_numero} marcada como excluída.")
            
            return {
                "success": True, 
                "message": f"Ordem de serviço {os_numero} deletada com sucesso.",
                "data": {"os_numero": os_numero, "negocio_idx": negocio_idx}
            }
            
        except Exception as e:
            logger.error(f"❌ Erro durante a exclusão no Neo4j para OS {os_numero}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False, 
                "error": "Erro interno",
                "message": f"Erro ao deletar ordem de serviço {os_numero}: {str(e)}", 
                "data": {}
            }





@router.post("/search/all")
async def search_all_workorders(data: dict):
    """
    Endpoint para buscar ordens de serviço com busca inteligente baseada nos filtros fornecidos.
    """
    logger.info(f"Endpoint /search/all chamado com: {data}")
    agent = AgentWorkOrder()
    resultado = await agent.search_all_workorders(data)
    logger.info(f"Retornando: {resultado}")
    return resultado


@router.post("/update")
async def update_workorder(data: dict):
    """
    Endpoint para atualizar uma ordem de serviço (status, prioridade, etc.)
    """
    logger.info(f"Endpoint /update chamado com: {data}")
    agent = AgentWorkOrder()
    resultado = await agent.update_workorder(data)
    logger.info(f"Retornando: {resultado}")
    return resultado


@router.post("/delete")
async def delete_workorder(data: dict):
    """
    Endpoint para deletar uma ordem de serviço
    """
    logger.info(f"Endpoint /delete")
    agent = AgentWorkOrder()
    resultado = await agent.delete_workorder(data)
    logger.info(f"Retornando: {resultado    }")
    return resultado


if __name__ == "__main__":
    import asyncio

    async def test_search_all():
        logger.info("===== test_products_search_all() - INÍCIO =====")
        workw = AgentWorkOrder()
        negocio_idx = "4015743441"
        data = {
            "negocio_idx": negocio_idx,
            "text": "",
            "limit": 0,
            "offset": 0
        }
        logger.info(f"data: {data}")
        result = await workw.search_all_workorders(data)
        logger.info(f"result: {result}")

    asyncio.run(test_search_all())