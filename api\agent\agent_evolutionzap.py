import requests, json
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import PlainTextResponse
from .agent_logger import <PERSON><PERSON><PERSON><PERSON>
from .agent_gptalkzap import Agent<PERSON><PERSON><PERSON><PERSON><PERSON>
from .agent_secret import Secret
from evolutionapi.client import EvolutionClient
from evolutionapi.models.message import TextMessage
import requests

import asyncio

logger = AgentLogger()
router = APIRouter()
secret = Secret()


# Configuração Evolution API
import os
logger.info(f"ENV: {os.getenv('ENV')}")
if os.getenv("ENV") == "production":
    EVOLUTION_API_BASE_URL: str = "https://evolutionapi.server.gptalk.com.br"
    EVOLUTION_API_INSTANCE_ID: str = secret.get_secret("EVOLUTION_API_INSTANCE_ID")
    EVOLUTION_API_INSTANCE_TOKEN: str = secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN")
    EVOLUTION_API_KEY = secret.get_secret("EVOLUTION_API_KEY")
else:
    EVOLUTION_API_BASE_URL: str = "http://localhost:8081"
    EVOLUTION_API_INSTANCE_ID: str = secret.get_secret("EVOLUTION_API_INSTANCE_ID") + "local"
    EVOLUTION_API_INSTANCE_TOKEN: str = secret.get_secret("EVOLUTION_API_INSTANCE_TOKEN_LOCAL")
    EVOLUTION_API_KEY = secret.get_secret("EVOLUTION_API_KEY_LOCAL")







class AgentEvolutionZap:
    def __init__(self):
        self.gptalkzap = AgentGptalkZap()
        # Se precisar de autenticação, adicione aqui

    async def process_message(self, body: dict):
        """
        Processa mensagens recebidas via webhook da Evolution API
        Exemplo de mensagem:
        {
          "event": "messages.upsert",
          "instance": "gptalk-zap",
          "data": {
            "key": {
              "remoteJid": "<EMAIL>",
              "fromMe": false,
              "id": "3FDEDF7DC4BBBC378670",
              "senderLid": "10574914130139@lid"
            },
            "pushName": "Carlos Silva",
            "status": "DELIVERY_ACK",
            "message": {
              "messageContextInfo": {
                "deviceListMetadata": {
                  "senderKeyHash": "yuIam/SmpYXU9w==",
                  "senderTimestamp": "1752103481",
                  "recipientKeyHash": "/Sinx0qrkl2V9g==",
                  "recipientTimestamp": "1752742155"
                },
                "deviceListMetadataVersion": 2
              },
              "conversation": "oi"
            },
            "contextInfo": {
              "expiration": 0,
              "ephemeralSettingTimestamp": "0",
              "disappearingMode": {
                "initiator": "CHANGED_IN_CHAT"
              }
            },
            "messageType": "conversation",
            "messageTimestamp": 1752798714,
            "instanceId": "a8e09742-faaf-457b-bfc5-19ef9591dbe8",
            "source": "desktop"
          },
          "destination": "https://297b6f2afc9b.ngrok-free.app/api/agent/evolutionzap/webhook/gptalk-zap",
          "date_time": "2025-07-17T21:31:53.183Z",
          "sender": "<EMAIL>",
          "server_url": "http://localhost:8080",
          "apikey": "AC491208E1AC-425C-9971-60C3FC144EDF"
        }
        """
        logger.info(f"process_message: ()")
        try:
            # Exemplo: processa mensagem recebida via webhook Evolution API

            data = body.get("data", {})
            

            if body.get("event") == "messages.upsert":
                if data.get("key", {}).get("fromMe", False):
                    return None

                message_type = data.get("messageType", "")
                if message_type in ["ack", "reaction", "delete"]:
                    return None

                sender_name = data.get("pushName", "Nome não disponível")
                sender_phone = data.get("key", {}).get("remoteJid", "").replace("@s.whatsapp.net", "")

                message_content = ""
                if message_type == "conversation":
                    message_content = data.get("message", {}).get("conversation", "")
                elif message_type == "extendedTextMessage":
                    message_content = body.get("message", {}).get("extendedTextMessage", {}).get("text", "")
                elif message_type == "imageMessage":
                    caption = body.get("message", {}).get("imageMessage", {}).get("caption", "")
                    message_content = f"[IMAGEM] {caption}" if caption else "[IMAGEM]"
                elif message_type == "audioMessage":
                    message_content = "[ÁUDIO]"
                elif message_type == "videoMessage":
                    caption = body.get("message", {}).get("videoMessage", {}).get("caption", "")
                    message_content = f"[VÍDEO] {caption}" if caption else "[VÍDEO]"
                elif message_type == "documentMessage":
                    filename = body.get("message", {}).get("documentMessage", {}).get("fileName", "documento")
                    message_content = f"[DOCUMENTO] {filename}"
                else:
                    message_content = f"[{message_type.upper()}]"

                self._log_message_info(sender_name, sender_phone, message_content)

                if message_type in ["conversation"] and message_content.strip():
                    data = {
                        "from": sender_phone,
                        "to": sender_phone,
                        "message": message_content,
                        "type": message_type,
                        "sender_name": sender_name,
                        "sender_phone": sender_phone,
                    }
                    result = await self.gptalkzap.send(data)
                    logger.info(f"result do agente gptalkzap: {result}")
                    return result
                else:
                    return None
            else:
                return None
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {str(e)}")

    def _log_message_info(self, sender_name: str, sender_phone: str, message_content: str):
        # Log simples
        pass

    async def send_evolution_message(self, to_number: str, message_text: str):
        #logger.info(f"Enviando mensagem para {to_number}: {message_text}")
        try:
            instance_id = EVOLUTION_API_INSTANCE_ID
            instance_token = EVOLUTION_API_INSTANCE_TOKEN
            
            # Usar EVOLUTION_API_KEY como api_token
            client = EvolutionClient(base_url=EVOLUTION_API_BASE_URL, api_token=EVOLUTION_API_KEY)
            
            message = TextMessage(
                number=to_number,
                text=message_text
            )
            
            response = client.messages.send_text(instance_id, message, instance_token)
            return response
        except Exception as e:
            logger.error(f"Erro ao enviar mensagem Evolution API: {e}")
            raise

    async def send_evolution_media(self, to_number: str, media_url: str, media_type: str, caption: str = ""):
        try:
            instance_id = EVOLUTION_API_INSTANCE_ID
            url = f"{EVOLUTION_API_BASE_URL}/message/sendMedia/{instance_id}"
            payload = {
                "number": to_number,
                "mediatype": media_type,
                "fileName": "image.jpg",  # Nome padrão, pode ser ajustado
                "caption": caption,
                "media": media_url
            }
            headers = {
                "apikey": EVOLUTION_API_KEY,
                "Content-Type": "application/json"
            }
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao enviar mídia Evolution API: {e}")
            raise

    async def get_instance_status(self):
        try:
            url = f"{EVOLUTION_API_BASE_URL}"
            response = requests.get(url)
            response.raise_for_status()
            status_data = response.json()
            #logger.info(f"Status da instância: {status_data}")
            return status_data
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao verificar status da instância: {e}")
            raise


@router.get("/webhook/{instance_id}")
async def webhook_verification(instance_id: str, request: Request):
    return {"status": "webhook evolution verified: " + instance_id}


@router.post("/webhook/{instance_id}")
async def handle_evolution_webhook(instance_id: str, request: Request):
    
    try:
        # Passo 1: Receber e logar o corpo da requisição
        body = await request.json()
        
        # Passo 2: Criar instância do AgentEvolutionZap
        evolution_api = AgentEvolutionZap()
        
        # Passo 3: Processar mensagem através do process_message
        result = await evolution_api.process_message(body)
        
        # Passo 5: Preparar dados para envio
        data = body.get("data", {})
        #logger.info(f"Dados extraídos do body: {json.dumps(data, indent=2)}")
        
        # Verificar se deve enviar resposta
        from_me = data.get("key", {}).get("fromMe", False)
        
        if result and not from_me:
            phone_number = data.get("key", {}).get("remoteJid", "").replace("@s.whatsapp.net", "")
            
            # Passo 7: Processar array de mensagens
            if result.get("messages") and isinstance(result["messages"], list):
                for i, message_data in enumerate(result["messages"]):
                    
                    try:
                        if message_data.get("media_url"):
                            
                            await evolution_api.send_evolution_media(
                                phone_number,
                                message_data["media_url"],
                                message_data.get("media_type", "image"),
                                message_data.get("message", "")
                            )
                            
                        elif message_data.get("message"):
                            await evolution_api.send_evolution_message(phone_number, message_data["message"])
                            
                        # Delay entre mensagens
                        if i < len(result["messages"]) - 1:
                            await asyncio.sleep(0.5)
                            
                    except Exception as e:
                        continue
                        
            # Passo 8: Processar resposta única (caso não seja array)
            elif result.get("media_url"):
                await evolution_api.send_evolution_media(
                    phone_number,
                    result["media_url"],
                    result.get("media_type", "image"),
                    result.get("message", "")
                )
                
            elif result.get("message"):
                await evolution_api.send_evolution_message(phone_number, result["message"])
        
        return {"status": "success"}
        
    except Exception as e:
        import traceback
        return {"status": "error", "message": str(e)}

@router.get("/status-evolution")
async def get_status():
    try:
        evolution_api = AgentEvolutionZap()
        status = await evolution_api.get_instance_status()
        return status
    except Exception as e:
        logger.error(f"Erro ao obter status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-message-evolution")
async def send_message_endpoint(request: Request):
    try:
        body = await request.json()
        to_number = body.get("to")
        message = body.get("message")
        if not to_number or not message:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'message' são obrigatórios")
        evolution_api = AgentEvolutionZap()
        result = await evolution_api.send_evolution_message(to_number, message)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.error(f"Erro ao enviar mensagem: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-media-evolution")
async def send_media_endpoint(request: Request):
    try:
        body = await request.json()
        to_number = body.get("to")
        media_url = body.get("media_url")
        media_type = body.get("media_type", "document")
        caption = body.get("caption", "")
        if not to_number or not media_url:
            raise HTTPException(status_code=400, detail="Parâmetros 'to' e 'media_url' são obrigatórios")
        evolution_api = AgentEvolutionZap()
        result = await evolution_api.send_evolution_media(to_number, media_url, media_type, caption)
        return {"status": "success", "result": result}
    except Exception as e:
        logger.error(f"Erro ao enviar mídia: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Exemplo de uso (para testes)
if __name__ == "__main__":
    import asyncio
    async def teste_resposta_texto():
        evolution_api = AgentEvolutionZap()
        await evolution_api.send_evolution_message("553184198720", "Oi carlos, boa noite")
        status = await evolution_api.get_instance_status()
        #print(f"Status: {status}")

    async def teste_resposta_imagem():
        evolution_api = AgentEvolutionZap()
        status = await evolution_api.get_instance_status()
        phone_number = "553184198720"

        message = {
                    "status": "success",
                    "message": "Em que posso ajuda-lo?",
                    "media_url": "https://gptalk.com.br/app_phj/app/assistenciamk/imagens/cardzap.png",
                    "media_type": "image"
                }
            
        logger.info("=== PASSO 8: Processando resposta única com mídia ===")
        logger.info(f"Enviando mídia única: {message['media_url']}")
        result = await evolution_api.send_evolution_media(
            phone_number,
            message["media_url"],
            message.get("media_type", "image"),
            message.get("message", "")
        )
        logger.info(f"Resultado: {result}")


    #asyncio.run(teste_resposta_texto())
    asyncio.run(teste_resposta_imagem())
