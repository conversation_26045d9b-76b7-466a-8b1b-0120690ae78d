from pydantic import BaseModel, Field 
from typing import List, Literal, Optional 
from fastapi import APIRouter
from fastapi.responses import StreamingResponse, JSONResponse
from .agent_llm import LLM
from ..cache import cache
from ..functions.util import generate_unique_id
from .agent_openai import OpenAi
from agents import function_tool
from datetime import datetime
import pytz
from .agent_logger import AgentLogger
from .agent_message import Message
from .agent_secret import Secret
from threading import Lock
import json


logger = AgentLogger()
fuso_brasilia = pytz.timezone('America/Sao_Paulo')
messageChat = {}
messageChat_lock = Lock()
agentMessage = Message()
secret = Secret()

router = APIRouter()
oai = OpenAi()


class AgentNeo4j:
    def __init__(
        self,
        name="neo4j",
        usuario_nome=None, 
        usuario_idx=None,
        negocio_idx=None):
        data_hora = datetime.now(pytz.timezone('America/Sao_Paulo')).strftime("%Y-%m-%d %H:%M:%S")

        # 📊 LOGS DETALHADOS DE CONEXÃO

        self.name = name
        self.usuario_nome = usuario_nome
        self.usuario_idx = usuario_idx
        self.negocio_idx = negocio_idx
        
        self.instructions = f"""
        Você é um assistente especializado em Neo4j que ajuda a converter consultas em linguagem natural 
        para queries Cypher e executá-las no banco de dados de grafos.

        Suas principais responsabilidades:
        1. **Interpretar consultas em linguagem natural** e convertê-las para Cypher
        2. **Executar queries Cypher** no banco Neo4j usando as ferramentas apropriadas
        3. **Consultar o schema** do banco quando necessário para entender a estrutura
        4. **Apresentar resultados** de forma clara e organizada


        ## 🛠️ **Ferramentas Disponíveis:**

        ### `consultar_schema_neo4j()`
        - Use para entender a estrutura do banco (nós, relacionamentos, propriedades)
        - Consulta o schema definido no arquivo modelo_dados.py (formato JSON)
        - Sempre consulte o schema antes de criar queries complexas ou criar novos nós, consultar nós , relacionamentos e propriedades.
        - Essencial para queries que envolvem múltiplos tipos de nós

        ### `gera_idx()`
        - **FUNÇÃO OBRIGATÓRIA:** Gera identificador único (IDX) para novos nós
        - Use SEMPRE antes de criar novos nós com CREATE ou MERGE
        - Retorna um ID único de 10 dígitos baseado em timestamp + número aleatório
        - **REGRA:** Todo nó criado DEVE ter a propriedade `idx` com valor único

        ### `executar_cypher_leitura(query, params)`
        - Para queries de **consulta** (MATCH, RETURN, WHERE, ORDER BY, LIMIT)
        - Use quando precisar buscar ou listar dados
        - Exemplos: buscar usuários, listar relacionamentos, estatísticas

        ### `executar_cypher_escrita(query, params)`
        - Para queries de **modificação** (CREATE, MERGE, SET, DELETE, REMOVE)
        - Use quando precisar criar, atualizar ou remover dados
        - **CUIDADO:** Estas operações modificam permanentemente o banco
        - **REGRA OBRIGATÓRIA:** Sempre use gera_idx() e adicione propriedade `idx` em novos nós

        ### 🧩 **APOC Plugin Disponível**
        - O plugin **APOC** está instalado e disponível no banco Neo4j.
        - Você pode utilizar qualquer função, procedimento ou utilitário do APOC quando necessário para enriquecer, transformar ou facilitar queries Cypher.
        - Exemplos de uso: manipulação de listas, datas, conversão de tipos, chamadas HTTP, importação/exportação, utilitários de grafos, etc.
        - Consulte a [documentação oficial do APOC](https://neo4j.com/labs/apoc/4.4/) para detalhes e exemplos de uso.

        ## 📋 **Processo de Trabalho:**

        1. **Analise** a solicitação do usuário
        2. **Consulte o schema** se necessário (especialmente para queries complexas)
        3. **Para criação de nós:** SEMPRE use gera_idx() primeiro para obter IDX único
        4. **Converta** a linguagem natural para Cypher apropriado (incluindo idx nos nós)
        5. **Execute** a query usando a ferramenta correta (leitura vs escrita)
        6. **Apresente** os resultados de forma clara

        ## ⚠️ **Diretrizes Importantes:**

        - **OBRIGATÓRIO:** Todo nó criado deve ter propriedade `idx` com valor único
        - **SEMPRE** use gera_idx() antes de CREATE ou MERGE de novos nós
        - **Sempre** valide a sintaxe Cypher antes de executar
        - **Prefira** consultar o schema para queries desconhecidas
        - **Use parâmetros** nas queries quando apropriado para segurança
        - **Explique** o que cada query faz antes de executá-la
        - **Seja cuidadoso** com operações de escrita - elas são permanentes

        ## 🚨 **Exemplo de Criação de Nó Correto:**
        ```
        1. Primeiro: gera_idx() → retorna idx: "1234567890"
        2. Depois: CREATE (n:Usuario {{"idx": "1234567890", "nome": "João", "email": "<EMAIL>"}})
        ```

        ## 👤 **Contexto da Sessão:**
        - Usuário: {usuario_nome}
        - Usuario_idx: {usuario_idx}
        - Data/Hora: {data_hora}
        - Negócio: {negocio_idx}

        O usuário é uma Pessoa no banco de dados.  E o usuario_idx corresponde a proopriedade idx da Pessoa. Desta forma, qualquer pesquisa ou inserção de dados do usuario no banco de dados deve-se levar em consideracao a Pessoa que o representa. 

        Responda sempre em português brasileiro e seja didático nas explicações.
        """

    def get_instructions(self):
        return self.instructions

    async def execute_read_query(self, query: str, params: dict = None):
        """
        Executa uma query Cypher de LEITURA no banco Neo4j REAL
        Este método é usado pela classe User e outras classes para executar queries
        """
        try:
            
            # Usar função MCP que já existe e funciona
            from ..functions.mcp_functions import mcp_neo4j_read_neo4j_cypher


            
            # Executar query usando a função MCP real
            resultado = mcp_neo4j_read_neo4j_cypher(query, params or {})
            
            
            return resultado
                
        except Exception as e:
            logger.error(f"❌ Erro geral em execute_read_query: {e}")
            import traceback
            logger.error(f"❌ Traceback completo: {traceback.format_exc()}")
            return [{"erro": f"Erro geral: {str(e)}"}]

    async def execute_write_query(self, query: str, params: dict = None):
        """
        Executa uma query Cypher de ESCRITA no banco Neo4j REAL
        Este método é usado para operações que modificam dados
        """
        try:
            
            # Validar se é realmente uma query de escrita
            write_keywords = ["CREATE", "MERGE", "SET", "DELETE", "REMOVE", "DROP"]
            if not any(keyword in query.upper() for keyword in write_keywords):
                error_msg = "Query de escrita deve usar CREATE, MERGE, SET, DELETE, REMOVE ou DROP"
                logger.error(f"❌ {error_msg}")
                return {"erro": error_msg}
            
            # Usar função MCP que já existe e funciona
            from ..functions.mcp_functions import mcp_neo4j_write_neo4j_cypher
            
            # Executar query de escrita usando a função MCP real
            resultado = mcp_neo4j_write_neo4j_cypher(query, params or {})
            
            return resultado
                
        except Exception as e:
            logger.error(f"❌ Erro geral em execute_write_query: {e}")
            return {"erro": f"Erro geral: {str(e)}"}




# ===============================================================================
# FUNÇÕES DE FERRAMENTAS (TOOLS) PARA O AGENTE NEO4J
# ===============================================================================

@function_tool
async def consultar_schema_neo4j():
    """
    Consulta e retorna o schema do banco de dados Neo4j a partir do arquivo modelo_dados.py.
    
    Esta função lê o schema definido no arquivo JSON modelo_dados.py que contém:
    - Todos os nós (labels) e suas propriedades
    - Relacionamentos entre os nós
    - Diretrizes e conceitos do modelo de dados
    
    Use esta função sempre que precisar entender a estrutura do grafo
    antes de criar queries Cypher.
    """
    try:
        
        # Ler o arquivo modelo_dados.py existente
        import json
        import os
        
        modelo_path = os.path.join(os.path.dirname(__file__), '..', 'modelo_dados.py')
        with open(modelo_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Extrair o JSON do arquivo Python
            start = content.find('{')
            end = content.rfind('}') + 1
            schema_json = content[start:end]
            schema_data = json.loads(schema_json)
        
        
        # Extrair informações relevantes do schema
        nodes = schema_data.get("nodes", {})
        general_guidelines = schema_data.get("general_guidelines", {})
        
        # Criar lista de labels disponíveis
        labels = []
        for node_name, node_info in nodes.items():
            node_labels = node_info.get("labels", [])
            labels.extend(node_labels)
        
        # Remover duplicatas e ordenar
        labels = sorted(list(set(labels)))
        
        
        return {
            "success": True,
            "schema": {
                "nodes": nodes,
                "labels": labels,
                "guidelines": general_guidelines,
                "total_nodes": len(nodes),
                "total_labels": len(labels)
            },
            "message": "Schema do modelo de dados obtido com sucesso do arquivo JSON. Use essas informações para criar queries Cypher adequadas."
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao consultar schema do modelo de dados: {e}")
        return {
            "success": False,
            "message": f"Erro ao consultar schema: {str(e)}"
        }


@function_tool
async def executar_cypher_leitura(query: str, params: Optional[dict] = None):
    """
    Executa uma query Cypher de LEITURA no banco Neo4j.
    
    Use esta função para queries que CONSULTAM dados, como:
    - MATCH para buscar nós e relacionamentos
    - RETURN para retornar resultados
    - WHERE para filtrar resultados
    - ORDER BY, LIMIT, SKIP para organizar resultados
    
    Args:
        query (str): Query Cypher de leitura (deve começar com MATCH)
        params (dict, optional): Parâmetros para a query
    
    IMPORTANTE: Apenas queries de leitura são permitidas (MATCH, RETURN, etc.).
    Queries de escrita (CREATE, MERGE, SET, DELETE) devem usar executar_cypher_escrita().
    """
    try:
        
        # Usar função MCP que já existe e funciona
        from ..functions.mcp_functions import mcp_neo4j_read_neo4j_cypher
        
        # Executar a query usando a função MCP real
        resultado = mcp_neo4j_read_neo4j_cypher(query, params or {})
        
        return {
            "success": True,
            "query": query,
            "params": params,
            "resultado": resultado,
            "message": "Query de leitura executada com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ ERRO GERAL ao executar query de leitura: {e}")
        import traceback
        logger.error(f"❌ Traceback completo: {traceback.format_exc()}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de leitura: {str(e)}"
        }


@function_tool
async def gera_idx():
    """
    Gera um identificador único (IDX) para ser usado como propriedade dos nós no Neo4j.
    
    Esta função utiliza generate_unique_id() para criar um identificador único
    baseado em timestamp e número aleatório.
    
    Returns:
        str: Identificador único no formato de 10 dígitos
    
    Use esta função sempre que precisar criar um novo nó no Neo4j para
    garantir que ele tenha uma propriedade idx única.
    """
    try:
        
        # Importar e usar a função generate_unique_id existente
        from ..functions.util import generate_unique_id
        
        # Gerar o IDX único
        novo_idx = generate_unique_id()
        
        
        return {
            "success": True,
            "idx": novo_idx,
            "message": f"IDX único gerado: {novo_idx}"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao gerar IDX: {e}")
        return {
            "success": False,
            "message": f"Erro ao gerar IDX: {str(e)}"
        }


@function_tool
async def executar_cypher_escrita(query: str, params: Optional[dict] = None):
    """
    Executa uma query Cypher de ESCRITA no banco Neo4j.
    
    Use esta função para queries que MODIFICAM dados, como:
    - CREATE para criar novos nós e relacionamentos
    - MERGE para criar ou encontrar nós/relacionamentos
    - SET para atualizar propriedades
    - DELETE para remover nós e relacionamentos
    - REMOVE para remover propriedades ou labels
    
    Args:
        query (str): Query Cypher de escrita (CREATE, MERGE, SET, DELETE, etc.)
        params (dict, optional): Parâmetros para a query
    
    IMPORTANTE: Apenas queries de escrita são permitidas. 
    Para consultas, use executar_cypher_leitura().
    
    ATENÇÃO: Estas operações modificam permanentemente o banco de dados.
    """
    try:
        
        # Usar função MCP que já existe e funciona
        from ..functions.mcp_functions import mcp_neo4j_write_neo4j_cypher
        
        # Executar query de escrita usando a função MCP real
        resultado = mcp_neo4j_write_neo4j_cypher(query, params or {})
        
        
        return {
            "success": True,
            "query": query,
            "params": params,
            "resultado": resultado,
            "message": "Query de escrita executada com sucesso"
        }
        
    except Exception as e:
        logger.error(f"❌ Erro ao executar query de escrita: {e}")
        return {
            "success": False,
            "query": query,
            "params": params,
            "message": f"Erro ao executar query de escrita: {str(e)}"
        }


# ===============================================================================
# FUNÇÕES UTILITÁRIAS PARA GERENCIAMENTO DE CONVERSAS
# ===============================================================================

def get_conversa_key(negocio_idx: str, agente_nome: str, conversa_idx: str = None) -> str:
    """Retorna a chave da conversa no cache"""
    return f"conversa_{negocio_idx}_{agente_nome}_{conversa_idx}"


def find_active_conversation(negocio_idx: str, agente_nome: str) -> tuple:
    """Procura por uma conversa ativa do usuário no cache"""
    for key in cache.keys():
        if key.startswith(f"conversa_{negocio_idx}_{agente_nome}_"):
            conversa_idx = key.split('_')[3]
            return conversa_idx, cache[key]
    return None, None


def add_message_to_history(history: list, message: str, is_user: bool = True) -> list:
    """Adiciona uma mensagem ao histórico no formato padrão de chat"""
    if not isinstance(history, list):
        history = []

    message_dict = {
        "role": "user" if is_user else "assistant",
        "content": str(message).strip()
    }

    history.append(message_dict)
    return history


# ===============================================================================
# ENDPOINTS DA API
# ===============================================================================

@router.post("/send/text")
async def send_text_neo4j(data: dict):
    """
    Endpoint principal para processar mensagens de texto para o agente Neo4j
    Converte linguagem natural em queries Cypher e executa no banco
    """
    
    try:
        # Extrair dados necessários
        mensagem = data.get("mensagem", "")
        modelo = data.get("modelo") or "1234567895"
        usuario_nome = data.get("usuario_nome", "Neo4j User")
        negocio_idx = data.get("negocio_idx", "")
        usuario_idx = data.get("usuario_idx")
        agente_flag = data.get("agente", None)

        if not mensagem:
            return {"success": False, "message": "Mensagem é obrigatória"}

        if not modelo:
            return {"success": False, "message": "Modelo é obrigatório"}

        if usuario_idx != "1122334455":
            return {"success": False, "message": "Você não tem permissão para usar este agente"}

        # Verificar se existe conversa ativa
        conversa_idx, historico_mensagens = find_active_conversation(negocio_idx, "neo4j")

        if not conversa_idx:
            conversa_idx = generate_unique_id()
            historico_mensagens = []

        # Adicionar mensagem do usuário ao histórico
        historico_mensagens = add_message_to_history(historico_mensagens, mensagem, True)

        # Criar agente Neo4j
        agentNeo4j = AgentNeo4j(
            negocio_idx=negocio_idx,
            usuario_nome=usuario_nome
        )

        # Carregar modelo
        llm = LLM()
        model = llm.get_model_idx(modelo)

        instructions = agentNeo4j.get_instructions()

        # Configurar agente
        agente_config = {
            "name": "Neo4j Query Agent",
            "instructions": instructions,
            "model": model,
            "tools": [
                consultar_schema_neo4j,
                gera_idx, 
                executar_cypher_leitura,
                executar_cypher_escrita,
                
            ],
            "handoff_description": None,
            "handoffs": [],
            "output_type": None,
            "input_guardrails": [],
            "output_guardrails": [],
        }

        # Criar o agente
        #logger.info(f"🔧 Criando agente Neo4j com configurações: {agente_config}")
        logger.info("vou criar o agente")
     
        agente_obj = await oai.agent_create(**agente_config)
        

        # Se for um agente (flag presente), retorna resposta no padrão status/messages/message
        if agente_flag:
            resposta_completa = ""
            try:
                async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                    resposta_completa += chunk_str
                # Adicionar resposta ao histórico
                historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
                # Salvar histórico no cache
                cache_key = get_conversa_key(negocio_idx, "neo4j", conversa_idx)
                cache[cache_key] = historico_mensagens
                # Tenta decodificar resposta_completa como JSON
                try:
                    resposta_json = json.loads(resposta_completa)
                    # Se for dict e tiver 'status' e 'messages', retorna direto
                    if isinstance(resposta_json, dict) and 'status' in resposta_json and 'messages' in resposta_json:
                        return resposta_json
                except Exception:
                    pass
                # Se não for JSON válido ou não tiver o formato esperado, encapsula
                return {
                            "status": "success",
                            "message": resposta_completa
                        }
            except Exception as e:
                logger.error(f"Erro durante execução sem streaming: {str(e)}")
                return {
                    "status": "fail",
                    "messages": [
                        {
                            "status": "fail",
                            "message": f"Erro durante execução sem streaming: {str(e)}"
                        }
                    ]
                }

        # Caso contrário, retorna streaming
        async def event_stream():
            resposta_completa = ""
            nonlocal historico_mensagens  # ← CORREÇÃO: Permite acesso à variável do escopo externo
            try:
                async for chunk in oai.agent_run(agente_obj, historico_mensagens):
                    if isinstance(chunk, bytes):
                        chunk_str = chunk.decode('utf-8')
                    else:
                        chunk_str = str(chunk)
                    resposta_completa += chunk_str
                    yield chunk_str
                # Adicionar resposta ao histórico
                historico_mensagens = add_message_to_history(historico_mensagens, resposta_completa, False)
                # Salvar histórico no cache
                cache_key = get_conversa_key(negocio_idx, "neo4j", conversa_idx)
                cache[cache_key] = historico_mensagens
            except Exception as stream_error:
                logger.error(f"Erro durante o streaming: {str(stream_error)}")
                yield f"Erro: {str(stream_error)}"

        return StreamingResponse(event_stream(), media_type="text/plain")

    except Exception as e:
        logger.error(f"Erro no endpoint send_text_neo4j: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "message": f"Erro interno: {str(e)}"}


@router.post("/drop/messages")
async def drop_messages_neo4j(data: dict):
    """
    Endpoint para limpar cache de conversas do agente Neo4j
    
    Parâmetros:
    - negocio_idx (str): ID do negócio (obrigatório)
    """
    
    negocio_idx = data.get("negocio_idx", "")
    
    if not negocio_idx:
        return {"success": False, "message": "negocio_idx é obrigatório"}
    
    try:
        chaves_removidas = 0
        chaves_para_remover = []
        
        # Buscar chaves do cache que correspondem ao agente Neo4j
        for key in list(cache.keys()):
            # Filtrar por conversas do agente neo4j
            if key.startswith(f"conversa_{negocio_idx}_neo4j_"):
                chaves_para_remover.append(key)
        
        # Remover as chaves identificadas do cache
        for key in chaves_para_remover:
            try:
                del cache[key]
                chaves_removidas += 1
            except KeyError:
                logger.warning(f"Chave já não existe no cache: {key}")
        
        
        return {
            "success": True,
            "message": f"Cache de conversas Neo4j limpo com sucesso! {chaves_removidas} conversas removidas.",
            "detalhes": {
                "conversas_removidas": chaves_removidas,
                "filtro_usado": f"negocio_idx: {negocio_idx}"
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {str(e)}")
        return {
            "success": False,
            "message": f"Erro ao limpar cache: {str(e)}"
        } 



if __name__ == "__main__":
    import asyncio

    async def  teste():
        data = {
            "mensagem": "oi",
            "negocio_idx": "7600786155",
            "modelo": "1234567895",
            "usuario_idx": "4344140157",
            "agente": "@gptalkzap",
            "usuario_idx": "1122334455"
       
        }

        result = await send_text_neo4j(data)
        print("result", result)

    asyncio.run(teste())

        
        