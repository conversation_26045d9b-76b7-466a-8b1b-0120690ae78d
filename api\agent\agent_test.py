import os
from openai import OpenAI
from agents import OpenAIChatCompletionsModel
from agents import Agent
from .agent_secret import Secret
secret = Secret()

google_api_key = secret.get_secret("GOOGLE_API_KEY")
# --- Configuração da API Key e do Cliente OpenAI ---
# Certifique-se de que sua GOOGLE_API_KEY esteja definida como uma variável de ambiente
# ou substitua os.getenv() pela sua chave diretamente (NÃO RECOMENDADO EM PRODUÇÃO)
#google_api_key = os.getenv("GOOGLE_API_KEY")

if not google_api_key:
    print("Erro: A variável de ambiente 'GOOGLE_API_KEY' não está definida.")
    print("Por favor, defina-a antes de executar o script.")
    exit()

# Crie uma instância do cliente OpenAI, apontando para o endpoint do Google Gemini
# Note que o base_url é o endpoint da API do Google Gemini
# A api_key é a sua chave do Google Gemini, mas é passada como 'api_key'
# porque o cliente OpenAI espera esse parâmetro.
client = OpenAI(
    api_key=google_api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta/models", # Endpoint da API do Google Gemini
)

# --- Criando o OpenAIChatCompletionsModel com Gemini ---
gemini_model = OpenAIChatCompletionsModel(
    model="gemini-2.5-flash",  # Nome do modelo Gemini
    openai_client=client      # Sua instância do cliente OpenAI (que aponta para Gemini)
)





print(f"Modelo configurado: {gemini_model.model_name}")

# --- Exemplo de uso com um Agente Simples ---
# Vamos criar um agente simples para interagir com o modelo Gemini
programmer_agent = Agent(
    role="Programmer",
    goal="Generate Python code snippets based on user requests.",
    backstory="You are an experienced Python programmer who is great at writing concise and efficient code.",
    llm=gemini_model, # O agente usará o modelo Gemini configurado
    max_iter=3, # Limite o número de iterações do agente para este exemplo simples
)

print("\n--- Teste de Geração de Código com o Agente ---")
try:
    # Use o agente para gerar código
    response = programmer_agent.execute_task(
        "Escreva uma função Python que calcule o fatorial de um número inteiro não negativo."
    )
    print("\nResposta do Agente (código Python):\n")
    print(response)

    print("\n--- Teste de Conversa Direta com o Modelo (sem Agente) ---")
    # Você também pode usar o cliente diretamente para uma interação mais básica,
    # embora o OpenAIChatCompletionsModel encapsule isso para o SDK de Agentes.
    stream = client.chat.completions.create(
        model="gemini-2.5-flash", # Especificamos o modelo novamente aqui
        messages=[{"role": "user", "content": "Me explique o conceito de recursão de forma simples."}],
        stream=True,
    )
    print("\nExplicação da Recursão:")
    for chunk in stream:
        if chunk.choices[0].delta.content is not None:
            print(chunk.choices[0].delta.content, end="")
    print() # Nova linha para o final

except Exception as e:
    print(f"\nOcorreu um erro: {e}")
    print("Por favor, verifique sua GOOGLE_API_KEY e a conectividade com a API do Google Gemini.")