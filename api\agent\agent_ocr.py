from agents import function_tool, OpenAIChatCompletionsModel
from fastapi import APIRouter
from .agent_llm import LLM
from .agent_secret import Secret
from .agent_pdf import Pdf
from .agent_image import Images
from .agent_openai import OpenAi
import json
router = APIRouter()

    

class Ocr:
    def __init__(self):
        pass

    async def document_accounting(self, data: dict):
        print("document_accounting()", data)
        print("data['url']", data["url"])
        secret = Secret()
        
        # Verificar se a extensão é PDF
        url = data.get("url", "")
        is_pdf = url.lower().endswith(".pdf")
        if is_pdf:
            pdf = Pdf()
            data["url"] = await  pdf.urlPdf_to_image_base64(url)
            #print("pdf_image:",pdf_image)
        else:
            print("nao é pdf")
            if "localhost" in data["host"]:
                print("localhost")
                data["url"] = await  Images.urlImage_to_base64(url)
                print("data[url]:",data["url"])
            else:
                print("nao é localhost")

        objeto_a_retornar = { 
                      "DOCUMENTO_TIPO": "", 
                      "A_PAGAR": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VALOR":0,
                        "VENCIMENTO": "",
                        "JA_PAGO": "",
                        "DATA_PAGAMENTO": ""
                          } 
                        ]
                      }
                      ,
                      "A_RECEBER":{
                        "TOTAL": 0, 
                        "NR_PARCELAS": 0,
                        "PARCELAS":[{
                        "VENCIMENTO": "",
                        "JA_RECEBIDO": "",
                        "DATA_PAGAMENTO": ""
                        }
                      ]}
                      ,
                      "DADOS_EMISSOR": { 
                        "RELACAO_TIPO": "",
                        "PESSOA_TIPO": "",
                        "RAZAO_SOCIAL": "",
                        "NOME_FANTASIA": "",
                        "CNPJ": "",
                        "CPF": "",
                        "TELEFONE": "",
                        "ENDEREÇO": { 
                            "CEP": "",
                            "LOGRADOURO": "",
                            "NUMERO": "",
                            "COMPLEMENTO": "",
                            "BAIRRO": "",
                            "CIDADE": "",
                            "UF": ""
                        }
                      },
                      "DISCRIMINAÇÃO": ""
                    }


        objeto_exemplo = { 
                      "DOCUMENTO_TIPO": "3-Conta de água", 
                      "A_PAGAR": {
                          "TOTAL": 100.00,
                          "NR_PARCELAS": 1,
                          "PARCELAS": [{
                          "VALOR": 100.00,
                          "VENCIMENTO": "01/04/25",
                          "JA_PAGO": "N",
                          "DATA_PAGAMENTO": ""
                          }]
                        },
                      "A_RECEBER": {
                        "TOTAL": 0,
                        "NR_PARCELAS": 0,
                        "PARCELAS": []
                      },
                      "DADOS_EMISSOR": { 
                          "RELACAO_TIPO": "2 - Fornecedor",
                          "PESSOA_TIPO": "1 - Jurídica",
                          "RAZAO_SOCIAL": "COPASA ABASTECIMENTOS S/A",
                          "NOME_FANTASIA": "COPASA",
                          "CNPJ": "37.753.978/0001-03",
                          "CPF": "",
                          "TELEFONE": "(31)984784825",
                          "ENDEREÇO": { 
                              "CEP": "31910720",
                              "LOGRADOURO": "RUA LIDIA",
                              "NUMERO": "125",
                              "COMPLEMENTO": "",
                              "BAIRRO": "CENTRO",
                              "CIDADE": "BELO HORIZONTE",
                              "UF": "MG"
                          }
                      },
                      "DISCRIMINAÇÃO": "CONTA DE MARÇO/25"
                    }


        query =  f"""
                    Preciso que analise o documento na imagem e preecha e retorne o objeto abaixo com os dados possiveis e encontrados no documento. Utilize as tabelas auxiliares para preecher 
                    
                    TABELAS AUXILIARES PARA CRIAÇÃO DO OBJETO
                    
                    Tabela para DOCUMENTO_TIPO:
                        1-boleto
                        2-Conta de Luz
                        3-Conta de água
                        4-Conta de internet
                        5-nota fiscal de compra de mercadorias para revenda
                        6-nota fiscal de serviços recebidos
                        7-nota fiscal de compra de compra de móveis
                        8-nota fiscal de compra de compra de equipamentos
                    	9-Contrato de prestação de serviço
                    	10-Recibo
                    	11-Cupom Fiscal    
                        
                        RELACAO_TIPO:
                        1 - Cliente
                        2 - Fornecedor
                        3 -Funcionário
                        4 - Sócio ou proprietário
                        
                        PESSOA_TIPO
                        1 - Jurídica
                        2 - Física
                        
                     OBSERVAÇÕES:
                     
                     Se nas chaves A_PAGAR ou  A_RECEBER não houverem dados, o valos deverá ser um array vazio: [] 
                     
                     **IMPORTANTE**: A resposta DEVE ser uma string JSON válida, utilizando aspas duplas (") para todas as chaves e valores de string. NÃO use aspas simples (').

                     Se o documento não for nenhum dos tipos acima, retorne o seguinte objeto:
                     {{"erro": "Não é um documento de contabilidade"}}

                     Se a imagem  for um documento e de um dos tipos acima, retorne o seguinte objeto:
                     
                     objeto a ser retornado:
                     {objeto_a_retornar}  
                      
                    
                    Exemplo do objto preenchido com os dados da imagem de uma conta de água:

                    {objeto_exemplo}


                   Somente retorne dados que existem no documento recebido.
                   Se o documento não for um dos tipos acima, retorne o seguinte objeto:
                   {{"erro": "Não é um documento de contabilidade"}}
                    
                    
                """




        data["query"] = query


        openai = OpenAi()
        result  = result = await openai.vision_query(data)
        return result

@router.post("/ocr/document/accounting")
async def ocr_document_accounting(data: dict):
    print("===== document_ocr() =====")

    ocr = Ocr()
    result = await ocr.document_accounting(data)
    return result




if __name__ == "__main__":

    import asyncio

    async def test_ocr_document():
        data = {
            "url": "https://gptalk.com.br/negocios/**********/documentos/contabilidade/0901795889a.png",
            "negocio_idx":"**********",
            "host":"localhost/gptalk"
        }

        #data["url"] = "https://gptalk.com.br/negocios/**********/imagens/contabilidade/**********.jpg"

        #data["url"] = "https://gptalk.com.br/imagens/logo.png"

        #data["url"] = "https://www.gptalk.com.br/negocios/**********/documentos/contabilidade/**********.pdf"
        result = await ocr_document_accounting(data)
        #print("result:",result)

    # Cria uma única instância do TestClient para ser reutilizada
    asyncio.run(test_ocr_document())