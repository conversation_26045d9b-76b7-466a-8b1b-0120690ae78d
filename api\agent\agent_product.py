from fastapi import APIRouter
from .agent_neo4j import AgentNeo4j
from .agent_logger import AgentLogger

router = APIRouter()
logger = AgentLogger()

class Product:
    def __init__(self):
        self.neo4j = AgentNeo4j()

    def _to_cypher_properties(self, data: dict, upper=False):
        """
        Converte as chaves do dicionário para caixa baixa (salvar) ou caixa alta (retorno)
        """
        if upper:
            return {k.upper(): v for k, v in data.items()}
        else:
            return {k.lower(): v for k, v in data.items()}

    async def create_products(self, data: dict):
        # Adiciona produtos no Neo4j
        results = []
        for record in data["adicionados"]:
            props = self._to_cypher_properties(record, upper=False)
            # Gera idx único
            idx_result = await self.neo4j.execute_read_query("CALL apoc.create.uuid() YIELD uuid RETURN uuid AS idx")
            idx = idx_result[0]["idx"] if idx_result and "idx" in idx_result[0] else None
            props["idx"] = idx
            # Cria o nó Produto
            cypher = """
            CREATE (p:Produto $props)
            RETURN p
            """
            params = {"props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "produtos": results}

    async def update_products(self, data: dict):
        # Atualiza produtos no Neo4j
        results = []
        for record in data["atualizados"]:
            props = self._to_cypher_properties(record, upper=False)
            idx = props.get("idx")
            if not idx:
                continue
            cypher = """
            MATCH (p:Produto {idx: $idx})
            SET p += $props
            RETURN p
            """
            params = {"idx": idx, "props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "produtos": results}

    async def products_fetch(self, business_idx: str):
        cypher = """
        MATCH (n:Negocio {idx: $business_idx})-[:VENDE_PRODUTO]->(p:Produto)
        WHERE p.excluido = 0 OR p.excluido IS NULL
        RETURN p
        ORDER BY p.nome
        """
        params = {"business_idx": business_idx}
        result = await self.neo4j.execute_read_query(cypher, params)
        produtos = []
        for r in result:
            p = r.get("p")
            if p and not isinstance(p, str) and hasattr(p, "properties"):
                produtos.append(self._to_cypher_properties(p.properties, upper=True))
        return produtos

    async def categories_fetch(self, business_id: str, colunas_nome: dict):
        cypher = """
        MATCH (c:ProdutoCategoria)-[:PERTENCE_AO_NEGOCIO]->(n:Negocio {idx: $business_id})
        WHERE c.excluido = 0 OR c.excluido IS NULL
        RETURN c
        ORDER BY c.nome
        """
        params = {"business_id": business_id}
        result = await self.neo4j.execute_read_query(cypher, params)
        categorias = []
        for r in result:
            c = r.get("c")
            if c and not isinstance(c, str) and hasattr(c, "properties"):
                categorias.append(self._to_cypher_properties(c.properties, upper=True))
        return categorias

    async def categories_add(self, data: dict):
        results = []
        for record in data['adicionados']:
            props = self._to_cypher_properties(record, upper=False)
            idx_result = await self.neo4j.execute_read_query("CALL apoc.create.uuid() YIELD uuid RETURN uuid AS idx")
            idx = idx_result[0]["idx"] if idx_result and "idx" in idx_result[0] else None
            props["idx"] = idx
            cypher = """
            CREATE (c:ProdutoCategoria $props)
            RETURN c
            """
            params = {"props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "categorias": results}

    async def categories_update(self, data: dict):
        results = []
        for record in data["atualizados"]:
            props = self._to_cypher_properties(record, upper=False)
            idx = props.get("idx")
            if not idx:
                continue
            cypher = """
            MATCH (c:ProdutoCategoria {idx: $idx})
            SET c += $props
            RETURN c
            """
            params = {"idx": idx, "props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "categorias": results}

    async def variant_fetch(self, produto_id: str, colunas_nome: dict):
        cypher = """
        MATCH (v:Produto)-[:E_VARIACAO_DE]->(p:Produto {idx: $produto_id})
        WHERE v.excluido = 0 OR v.excluido IS NULL
        RETURN v
        ORDER BY v.nome
        """
        params = {"produto_id": produto_id}
        result = await self.neo4j.execute_read_query(cypher, params)
        variacoes = []
        for r in result:
            v = r.get("v")
            if v and not isinstance(v, str) and hasattr(v, "properties"):
                variacoes.append(self._to_cypher_properties(v.properties, upper=True))
        return variacoes

    async def variant_add(self, data: dict):
        results = []
        for record in data["adicionados"]:
            props = self._to_cypher_properties(record, upper=False)
            idx_result = await self.neo4j.execute_read_query("CALL apoc.create.uuid() YIELD uuid RETURN uuid AS idx")
            idx = idx_result[0]["idx"] if idx_result and "idx" in idx_result[0] else None
            props["idx"] = idx
            produto_pai = props.get("idx_pai")
            cypher = """
            MATCH (p:Produto {idx: $produto_pai})
            CREATE (v:Produto $props)-[:E_VARIACAO_DE]->(p)
            RETURN v
            """
            params = {"props": props, "produto_pai": produto_pai}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "variacoes": results}

    async def variant_update(self, data: dict):
        results = []
        for record in data["atualizados"]:
            props = self._to_cypher_properties(record, upper=False)
            idx = props.get("idx")
            if not idx:
                continue
            cypher = """
            MATCH (v:Produto {idx: $idx})
            SET v += $props
            RETURN v
            """
            params = {"idx": idx, "props": props}
            result = await self.neo4j.execute_write_query(cypher, params)
            results.append(result)
        return {"success": True, "variacoes": results}

    async def categories_children_fetch(self, business_id: str):
        cypher = """
        MATCH (c:ProdutoCategoria)-[:PERTENCE_AO_NEGOCIO]->(n:Negocio {idx: $business_id})
        WHERE (c.excluido = 0 OR c.excluido IS NULL)
        AND NOT (c)<-[:E_SUBCATEGORIA_DE]-(:ProdutoCategoria)
        RETURN c
        ORDER BY c.nome
        """
        params = {"business_id": business_id}
        result = await self.neo4j.execute_read_query(cypher, params)
        categorias = []
        for r in result:
            c = r.get("c")
            if c and not isinstance(c, str) and hasattr(c, "properties"):
                categorias.append(self._to_cypher_properties(c.properties, upper=True))
        return categorias


    async def products_search_all(self, filters: dict):
        """Busca produtos de forma inteligente e flexível por NOME, CODIGO, DESCR  no Neo4j"""
        logger.info(f"===== products_search_all() (Neo4j) ===== {filters}")
        try:
            texto_busca = filters.get("text", "")
            negocio_idx = filters.get('negocio_idx', '')
            limit = filters.get('limit', 10)
            offset = filters.get('offset', 0)
            cypher = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:VENDE_PRODUTO]->(p:Produto)
            WHERE (p.excluido = 0 OR p.excluido IS NULL)
            """
            params = {"negocio_idx": negocio_idx}
            if texto_busca and texto_busca.strip():
                cypher += " AND (toLower(p.nome) CONTAINS toLower($texto_busca) OR toLower(p.codigo) CONTAINS toLower($texto_busca) OR toLower(p.descr) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
            cypher += " RETURN p ORDER BY p.nome"
            if limit > 0:
                cypher += " SKIP $offset LIMIT $limit"
                params["offset"] = offset
                params["limit"] = limit
            result = await self.neo4j.execute_read_query(cypher, params)

            #logger.info(f"🔍 RESULTADO PARCIAL:\n{result}") 
            produtos = [{k.upper(): v for k, v in r["p"].items()} for r in result if "p" in r]
            resposta = {
                "success": True,
                "data": produtos,
                "total": len(produtos),
                "message": f"Encontrados {len(produtos)} produtos para '{texto_busca}'"
            }
            logger.info(f"🔍 RESULTADO FINAL:\n{resposta}")
            return resposta
        except Exception as e:
            logger.error(f"❌ ERRO em services_search_all: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }
            logger.info(f"🔍 SQL FINAL (com valores substituídos):\n{query_formatada}")
         

    async def products_search_all2(self, filters: dict):
        """Busca produtos de forma inteligente e flexível por NOME, CODIGO, DESCR no Neo4j"""
        logger.info(f"===== products_search_all() (Neo4j) ===== {filters}")
        try:
            texto_busca = filters.get("text", "")
            negocio_idx = filters.get('negocio_idx', '')
            limit = filters.get('limit', 10)
            offset = filters.get('offset', 0)
            
            cypher = """
            MATCH (n:Negocio {idx: $negocio_idx})-[:VENDE_PRODUTO]->(p:Produto)
            WHERE (p.excluido = 0 OR p.excluido IS NULL)
            """
            
            params = {"negocio_idx": negocio_idx}
            
            if texto_busca and texto_busca.strip():
                cypher += " AND (toLower(p.nome) CONTAINS toLower($texto_busca) OR toLower(p.codigo) CONTAINS toLower($texto_busca)     OR toLower(p.descr) CONTAINS toLower($texto_busca))"
                params["texto_busca"] = texto_busca
                
            cypher += """
            WITH p
            ORDER BY p.nome
            WITH collect(p) as produtos
            WITH produtos[$offset..($offset+$limit)] as produtos_paginados, size(produtos) as total
            UNWIND produtos_paginados as p
            RETURN apoc.map.fromPairs([k in keys(p) | [toUpper(k), p[k]]]) as produto, total
            """
            
            params["offset"] = offset
            params["limit"] = limit
            
            result = await self.neo4j.execute_read_query(cypher, params)
            
            logger.info(f"🔍 RESULTADO PARCIAL:\n{result}")

            
            # Agora cada registro já vem como dicionário com chaves maiúsculas
            produtos = [r["produto"] for r in result]
            total = result[0]["total"] if result else 0
            
            resposta = {
                "success": True,
                "data": produtos,
                "total": total,
                "message": f"Encontrados {len(produtos)} produtos para '{texto_busca}'"
            }
            return resposta
            
        except Exception as e:
            logger.error(f"❌ ERRO em services_search_all: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Erro interno do sistema",
                "data": [],
                "total": 0
            }

    async def products_search(self, business_id: str, keywords: str, columns: dict):
        cypher = """
        MATCH (n:Negocio {idx: $business_id})-[:POSSUI_PRODUTO]->(p:Produto)
        WHERE (p.nome CONTAINS $keywords OR p.codigo CONTAINS $keywords) AND (p.excluido = 0 OR p.excluido IS NULL)
        RETURN p
        ORDER BY p.nome
        """
        params = {"business_id": business_id, "keywords": keywords}
        result = await self.neo4j.execute_read_query(cypher, params)
        produtos = []
        for r in result:
            p = r.get("p")
            if p and not isinstance(p, str) and hasattr(p, "properties"):
                produtos.append(self._to_cypher_properties(p.properties, upper=True))
        return produtos

# Endpoints adaptados para Neo4j

@router.post("/create/products")
async def create_products(data: dict):
    product = Product()
    result = await product.create_products(data)
    return result

@router.post("/update/products")
async def update_products(data: dict):
    product = Product()
    result = await product.update_products(data)
    return result

@router.get("/categories/children/fetch")
async def categories_children_fetch(business_id: str):
    product = Product()
    result = await product.categories_children_fetch(business_id)
    return result

@router.post("/products/fetch")
async def products_fetch(business_idx: str):
    product = Product()
    result = await product.products_fetch(business_idx)
    return result

@router.post("/search/all")
async def products_search_all(data: dict):
    logger.info("===== products_search_all() - INÍCIO =====")
    logger.info(f"Payload recebido: {data}")
    product = Product()
    try:
        result = await product.products_search_all(data)
        logger.info(f"Resultado retornado: {result}")
        return result
    except Exception as e:
        import traceback
        logger.error(f"Erro no endpoint /search/all: {str(e)}")
        logger.error(traceback.format_exc())
        return {"success": False, "error": str(e), "traceback": traceback.format_exc()}

@router.post("/products/search")
async def products_search(business_id: str, keywords: str, columns: dict):
    product = Product()
    result = await product.products_search(business_id, keywords, columns)
    return result

@router.post("/variant/update")
async def variant_update(variant: dict):
    product = Product()
    result = await product.variant_update(variant)
    return result

@router.post("/variant/add")
async def variant_add(variant: dict):
    product = Product()
    result = await product.variant_add(variant)
    return result

@router.post("/variant/fetch/{produto_id}")
async def variant_fetch(produto_id: str, colunas_nome: dict):
    product = Product()
    result = await product.variant_fetch(produto_id, colunas_nome)
    return result

@router.post("/categories/fetch/{business_id}")
async def categories_fetch(business_id: str, colunas_nome: dict):
    product = Product()
    result = await product.categories_fetch(business_id, colunas_nome)
    return result

@router.post("/categories/add")
async def categories_add(data: dict):
    product = Product()
    result = await product.categories_add(data)
    return result

@router.post("/categories/update")
async def categories_update(data: dict):
    product = Product()
    result = await product.categories_update(data)
    return result


if __name__ == "__main__":
    import asyncio

    async def test_products_search_all():
        logger.info("===== test_products_search_all() - INÍCIO =====")
        product = Product()
        negocio_idx = "4015743441"
        data = {
            "negocio_idx": negocio_idx,
            "text": "",
            "limit": 0,
            "offset": 0
        }
        logger.info(f"data: {data}")
        result = await product.products_search_all(data)
        #logger.info(f"result: {result}")

    asyncio.run(test_products_search_all())
   





