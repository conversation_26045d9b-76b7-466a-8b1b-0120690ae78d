versao = "2025.07.31-16.30"
import asyncio
import sys

# Configurar a política de loop de eventos para Windows se necessário
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from fastapi import Request, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import traceback
from dotenv import load_dotenv
import os
from datetime import datetime
from pathlib import Path

from .app import app,mcp
from .agent.agent_logger import AgentLogger
logger = AgentLogger()

# Garante logging em DEBUG e no console
logger.set_level(10)  # DEBUG
logger.force_console_logging()
import logging

# Configura os loggers de terceiros para evitar logs excessivos
logging.getLogger("httpcore").setLevel(logging.INFO)
logging.getLogger("httpx").setLevel(logging.INFO)

print("[main.py] Logger configurado para DEBUG e console.")
logger.info("[main.py] Teste INFO do AgentLogger")
logger.debug("[main.py] Teste DEBUG do AgentLogger")
logging.info("[main.py] Teste INFO do root logger")
logging.debug("[main.py] Teste DEBUG do root logger")

# Comentário orientando o uso correto do logger para futuras manutenções
# Para alterar o nível de logging, utilize o método set_level() do AgentLogger
# Exemplo: logger.set_level(20) para INFO, logger.set_level(10) para DEBUG

from .agent.agent_business import router as agent_business_router
from .agent.agent_customer import router as agent_customer_router
from .agent.agent_workOrder import router as agent_workorder_router
from .agent.agent_logger import router as agent_logger_router
from .agent.agent_task import router as agent_task_router
from .agent.agent_webdesigner import router as agent_webdesigner_router
from .agent.agent_whatsapp import router as agent_whatsapp_router
from .agent.agent_whatsappwhapi import router as agent_whatsappwhapi_router
from .agent.agent_megaapi import router as agent_megaapi_router
from .agent.agent_evolutionzap import router as agent_evolutionzap_router
from .agent.agent_gptalkzap import router as agent_gptalkzap_router
from .agent.agent_pagseguro import router as agent_pagseguro_router
from .agent.agent_googleoauth import router as agent_googleoauth_router
from .agent.agent_user import router as agent_user_router
from .agent.agent_email import router as agent_email_router
from .agent.agent_product import router as agent_product_router
from .agent.agent_service import router as agent_service_router
from .agent.agent_mktcontact import router as agent_mktcontact_router
from .agent.agent_seller import router as agent_seller_router   
from .agent.agent_cashflow import router as agent_cashflow_router
from .agent.agent_accountant import router as accountant_router
from .agent.agent_designer import router as agent_designer_router
from .agent.agent_atm import router as agent_atm_router
from .agent.agent_dre360 import router as agent_dre360_router
from .agent.agent_mcp import router as agent_mcp_router
from .agent.agent_mcplus import router as agent_mcplus_router
from .agent.agent_mysql import router as agent_mysql_router
from .agent.agent_mysql import Mysql
from .agent.agent_oficinatech import router as agent_oficinatech
from .agent.agent_assistenciamk import router as agent_assistenciamk_router
from .agent.agent_marykay import router as agent_marykay_router
from .agent.agent_message import router as agent_message_router
from .agent.agent_whisper import router as agent_whisper_router
from .agent.agent_neo4j import router as agent_neo4j_router
from .agent.agent_serverVps import router as agent_servervps_router

# Carrega variáveis do .env
load_dotenv()

# Configurar a política de loop de eventos para Windows se necessário
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())







# Middleware para capturar TODAS as requisições e respostas
@app.middleware("http")
async def log_requests(request: Request, call_next):
    # Ignora log de requisições para o endpoint tail (evita spam no log)
    if not (request.method == "GET" and "/tail" in str(request.url)):
        # Log da requisição
        logger.info(f"Requisição: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        # Log da resposta
        #logger.info(f"Resposta: Status {response.status_code}")
        return response
    except Exception as e:
        # Log do erro com traceback completo
        error_msg = f"""
        ERRO NA REQUISIÇÃO
        URL: {request.url}
        Método: {request.method}
        Erro: {str(e)}
        Traceback:
        {traceback.format_exc()}
        """
        logger.error(error_msg)
        raise

# Configuração do CORS Middleware com origens atualizadas
origins = [
    "http://localhost",
    "http://127.0.0.1",
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:8000",
    "http://localhost:8001",
    "http://127.0.0.1:8001",
    "http://localhost:8501",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:8501",
    "https://gptalk-server-uvu5.onrender.com",
    "https://gptalk-app-sl.onrender.com",
    "https://gptalk.com.br",
    "https://www.gptalk.com.br",
    "https://gptalk-app-sl-production-e8a7.up.railway.app",
    "https://gptalk-app-sl-f7suh.ondigitalocean.app",
    "https://gptalk-server-8jgy4.ondigitalocean.app",  # Novo domínio DigitalOcean
    "http://gptalk-server-8jgy4.ondigitalocean.app",   # Versão HTTP do domínio DigitalOcean
    "https://app.restauranteinteligente.gptalk.com.br",
    "https://www.app.restauranteinteligente.gptalk.com.br",
    "https://app.restauranteinteligente.gptalk.com.br",  # Versão HTTP
    "http://www.app.restauranteinteligente.gptalk.com.br",  # Versão HTTP
    "https://app.atm.gptalk.com.br",
    "https://www.app.atm.gptalk.com.br",
    "https://server.gptalk.com.br",
    "https://www.server.gptalk.com.br",
    "https://www.app.dre360.gptalk.com.br",
    "https://app.dre360.gptalk.com.br",
    "https://app.oficinatech.gptalk.com.br",
    "https://www.app.oficinatech.gptalk.com.br",
    "https://app.oficinatech.gptalk.com.br",  # Versão HTTP
    "https://www.app.oficinatech.gptalk.com.br",  # Versão HTTP
    
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_origin_regex=r"https://(.*\.)?gptalk\.com\.br$",  # Permite qualquer subdomínio gptalk.com.br
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],  # Simplificado para aceitar todos os headers
    expose_headers=["*"],
    max_age=600,
)

# Inclusão de Rotas
app.include_router(agent_business_router, prefix="/api/agent/business")
app.include_router(agent_logger_router, prefix="/api/agent/logger")
app.include_router(agent_task_router, prefix="/api/agent/task")
app.include_router(agent_whatsapp_router, prefix="/api/agent/whatsapp")
app.include_router(agent_whatsappwhapi_router, prefix="/api/agent/whatsappwhapi")
app.include_router(agent_megaapi_router, prefix="/api/agent/megaapi")
app.include_router(agent_evolutionzap_router, prefix="/api/agent/evolutionzap")
app.include_router(agent_gptalkzap_router, prefix="/api/agent/gptalkap")
app.include_router(agent_webdesigner_router, prefix="/api/agent/webdesigner")
app.include_router(agent_pagseguro_router, prefix="/api/agent/pagseguro")
app.include_router(agent_googleoauth_router, prefix="/api/agent/googleoauth")
app.include_router(agent_user_router, prefix="/api/agent/user")
app.include_router(agent_email_router, prefix="/api/agent/email")
app.include_router(agent_product_router, prefix="/api/agent/product")
app.include_router(agent_service_router, prefix="/api/agent/service")
app.include_router(agent_mktcontact_router, prefix="/api/agent/mktcontact")
app.include_router(agent_seller_router, prefix="/api/agent/seller")
app.include_router(agent_cashflow_router, prefix="/api/agent/cashflow")
app.include_router(accountant_router, prefix="/api/agent/accountant")
app.include_router(agent_designer_router, prefix="/api/agent/designer")
app.include_router(agent_atm_router, prefix="/api/agent/atm")
app.include_router(agent_dre360_router, prefix="/api/agent/dre360")
app.include_router(agent_mcp_router, prefix="/api/agent/mcp")   
app.include_router(agent_mcplus_router, prefix="/api/agent/mcplus")
app.include_router(agent_mysql_router, prefix="/api/agent/mysql")
app.include_router(agent_oficinatech, prefix="/api/agent/oficinatech")
app.include_router(agent_assistenciamk_router, prefix="/api/agent/assistenciamk")
app.include_router(agent_marykay_router, prefix="/api/agent/marykay")
app.include_router(agent_message_router, prefix="/api/agent/message")
app.include_router(agent_customer_router, prefix="/api/agent/customer")
app.include_router(agent_workorder_router, prefix="/api/agent/workorder")
app.include_router(agent_whisper_router, prefix="/api/agent/whisper")
app.include_router(agent_neo4j_router, prefix="/api/agent/neo4j")
app.include_router(agent_servervps_router, prefix="/api/agent/servervps")
@app.get("/")
async def route_handler():
    result = {"content": f"Versão: {versao}"}
    return result

# Exemplo de rota que pode gerar um erro para testar logging
@app.get("/error")
async def cause_error():
    try:
        1 / 0  # Causa um erro para testar logging
    except Exception as e:
        logger.error("Erro ocorrido: %s", e)
        return {"error": str(e)}

# Middleware para capturar exceções globais
@app.middleware("http")
async def catch_exceptions_middleware(request: Request, call_next):
    try:
        return await call_next(request)
    except Exception as e:
        # Log do erro com traceback completo
        error_msg = f"""
        ====== Erro não tratado ======
        URL: {request.url}
        Método: {request.method}
        Headers: {dict(request.headers)}
        Erro: {str(e)}
        Traceback:
        {traceback.format_exc()}
        ===========================
        """
        logger.error(error_msg)
        
        # Retorna uma resposta JSON com o erro
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Erro interno do servidor",
                "error_type": str(type(e).__name__),
                "error_message": str(e)
            }
        )

# Handler para erros de validação
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error_msg = f"""
    ====== Erro de Validação ======
    URL: {request.url}
    Método: {request.method}
    Erros: {exc.errors()}
    Body: {exc.body}
    ===========================
    """
    logger.error(error_msg)
    return JSONResponse(
        status_code=422,
        content={"detail": exc.errors()}
    )

# Handler para erros HTTP
@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    error_msg = f"""
    ====== Erro HTTP ======
    URL: {request.url}
    Método: {request.method}
    Status Code: {exc.status_code}
    Detail: {exc.detail}
    ===========================
    """
    logger.error(error_msg)
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )



@app.get("/run", operation_id="run_sql")
async def run(
    query: str = Query(..., description="A consulta SQL a ser executada")
):
    mysql = Mysql()
    result = await mysql.query(query)
    return result

mcp.setup_server()