import os
import sys
import signal
import subprocess
import threading

def start_uvicorn():
    print("start_uvicorn")
    command = [
        sys.executable, "-m", "uvicorn",
        "api.main:app",
        "--host", "127.0.0.1",
        "--port", "3000",
    ]
    print("command", command)
    subprocess.Popen(command)

threading.Thread(target=start_uvicorn).start()

def application(environ, start_response):
    print("application")
    status = '200 OK'
    print("status", status)
    output = b'Started Uvicorn server'
    response_headers = [('Content-type', 'text/plain'),
                        ('Content-Length', str(len(output)))]
    print("response_headers", response_headers)
    start_response(status, response_headers)
    return [output]
