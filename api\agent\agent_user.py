from .agent_mysql import Mysql
from .agent_neo4j import Agent<PERSON><PERSON>4j
from .agent_entity import Entity
from fastapi import APIRouter
from datetime import datetime, timedelta
import hashlib, time
from fastapi.testclient import TestClient
from .agent_email import Email
from functions.util.generate_unique_id import generate_unique_id
from .agent_logger import <PERSON><PERSON>ogger
from .agent_openai import OpenAi

logger = AgentLogger()
router = APIRouter()

class User(Entity):
    def __init__(self):
        self.mysql = Mysql()
        self.neo4j = AgentNeo4j()
       
        self.ID = 0
        self.NOME = None

    async def create_by_email_password(self,EMAIL:str,SENHA:str,TELEFONE:str,NOME:str, IDX:str):

       #Verifica se o email ja existe ou telefone já existe
        result = await self.fetch("ID,NOME,EMAIL,EMAIL_CONFIRMADO",[f"EMAIL='{EMAIL}'"])
        if (result and 'ERRO' not in result):
            return {"ERRO":"Email já cadastrado"}
        
        result = await self.fetch_telephone("ID,NOME,TELEFONE,TELEFONE_CONFIRMADO",TELEFONE)
        if (result and 'ERRO' not in result): 
            return {"ERRO":"Telefone já cadastrado"}


        data = {
            "EMAIL": EMAIL,
            "SENHA": SENHA,
            "TELEFONE": "55"+ TELEFONE,
            "DATA_CAD": datetime.now(),
            "NOME": NOME,
            "IDX": IDX
        }
        id = await self.add(data)
        data["ID"] = id
        return  {"SUCESSO":"Usuario criado com sucesso"}

    async def fetch(self,columns:str,filters:list):
        user = await self.mysql.fetch(columns,"USUARIO",filters)
       
        if user:
                user = user[0]
                for column in user.keys():
                    #print(column,user[column])
                    setattr(self, column, user[column])
                return user;
        else:
             return {"ERRO":"Usuario não encontrado"}
         
    async def fetch_telephone(self, colunas: str, telefone: str):

        """
        A query deverá buscar pelo numero do telefone com e sem o numero 9 adicional. se o numero tiver 11 digitos é pq ja tem o 9 adicional na terceira posição. Então o telefone2 deve ser o numero com o 9 retirado. Se o numero tiver 10 digitos é pq nao tem o novo adicional, entao o telefone 2 deve ser o numero com o 9 adicionado na terceira posição.
        """
        try:
            # Verifica se o telefone tem 10 ou 11 dígitos
            if len(telefone) not in [10, 11]:
                return {"ERRO":"Telefone inválido"}

            telefone2 = ""
            
            if len(telefone) == 11:
                # Se tem 11 dígitos, remove o 9 da terceira posição
                telefone2 = telefone[:2] + telefone[3:]
            elif len(telefone) == 10:
                # Se tem 10 dígitos, adiciona o 9 na terceira posição
                telefone2 = telefone[:2] + "9" + telefone[2:]

            # Converte as colunas solicitadas para a sintaxe do Neo4j
            # Se colunas for "*", retorna todas as propriedades principais
            if colunas == "*":
                return_clause = "u.id AS ID, u.nome AS NOME, u.email AS EMAIL, u.telefone AS TELEFONE, u.telefoneConfirmado AS TELEFONE_CONFIRMADO, u.idx AS IDX"
            else:
                # Mapeia as colunas SQL para propriedades Neo4j
                column_mapping = {
                    "ID": "u.id AS ID",
                    "NOME": "u.nome AS NOME", 
                    "EMAIL": "u.email AS EMAIL",
                    "TELEFONE": "u.telefone AS TELEFONE",
                    "TELEFONE_CONFIRMADO": "u.telefoneConfirmado AS TELEFONE_CONFIRMADO",
                    "IDX": "u.idx AS IDX"
                }
                
                colunas_list = [col.strip() for col in colunas.split(",")]
                return_parts = []
                for col in colunas_list:
                    if col in column_mapping:
                        return_parts.append(column_mapping[col])
                    else:
                        return_parts.append(f"u.{col.lower()} AS {col}")
                
                return_clause = ", ".join(return_parts)

            # Query Cypher para buscar pelos dois formatos de telefone
            query = f"""
            MATCH (u:Pessoa) 
            WHERE u.telefone = $telefone1 OR u.telefone = $telefone2
            RETURN {return_clause}
            LIMIT 1
            """

            logger.info(f"Buscando usuário por telefone: {telefone} ou {telefone2}")
            logger.info(f"Query: {query}")
            
            params = {"telefone1": telefone, "telefone2": telefone2}
            result = await self.neo4j.execute_read_query(query, params)
            
            logger.info(f"Resultado da busca por telefone: {result}")
            
            if result and len(result) > 0:
                usuario = result[0]
                # Define os atributos da instância
                for column in usuario.keys():
                    setattr(self, column, usuario[column])
                return usuario
            else:
                logger.info("Usuario não encontrado")
                return {"ERRO": "Usuario não encontrado"}
                
        except Exception as e:
            logger.error(f"Erro ao buscar usuário por telefone: {str(e)}")
            return {"ERRO": str(e)}

    async def add(self,data:dict):
        #print("agent_user.add",data)
        id = await self.mysql.add("USUARIO",data)
        self.ID = id
        return id

    
    
    async def update(self,data:dict):
         await self.mysql.update("USUARIO",data)  
        
    async def password_reset(self,token:str,new_password:str):
      usuario = await self.fetch("ID",["SENHA_TOKEN='{}'".format(token)])
      if 'ERRO' in usuario:
        return {"ERRO":"Token inválido ou expirado"}
      

      result = await self.mysql.query(f"UPDATE USUARIO SET SENHA='{new_password}' , SENHA_TOKEN = '' WHERE SENHA_TOKEN = '{token}'")
      return  {"SUCESSO":"Senha alterada com sucesso"}

    async def search_phone(self, phone: str):
        """
        Busca um usuário pelo número de telefone no Neo4j
        Busca com e sem o dígito 9 adicional e remove o prefixo 55 se existir
        Retorna idx, nome e email se encontrado
        """
        try:
            # Remove o prefixo 55 se existir, pois no banco os números são registrados sem o 55
            telefone_limpo = phone
            if phone.startswith("55"):
                telefone_limpo = phone[2:]
                logger.info(f"Removido prefixo 55: {phone} -> {telefone_limpo}")
            
            # Verifica se o telefone tem 10 ou 11 dígitos após remoção do 55
            if len(telefone_limpo) not in [10, 11]:
                return {
                    "found": False,
                    "message": "Telefone inválido - deve ter 10 ou 11 dígitos (após remoção do 55)"
                }

            # Gera o formato alternativo (com/sem o 9 adicional)
            telefone2 = ""
            if len(telefone_limpo) == 11:
                # Se tem 11 dígitos, remove o 9 da terceira posição
                telefone2 = telefone_limpo[:2] + telefone_limpo[3:]
            elif len(telefone_limpo) == 10:
                # Se tem 10 dígitos, adiciona o 9 na terceira posição
                telefone2 = telefone_limpo[:2] + "9" + telefone_limpo[2:]

            # Query Cypher para buscar pelos dois formatos de telefone
            query = """
            MATCH (p:Pessoa)
            WHERE p.telefone = $telefone OR p.telefone = $telefone_alt
            RETURN p.idx AS idx, p.nome AS nome, p.email AS email
            LIMIT 1
            """
            
            logger.info(f"Buscando usuário por telefone: {telefone_limpo} ou {telefone2}")
            logger.info(f"Query: {query}")
            
            params = {"telefone": telefone_limpo, "telefone_alt": telefone2}
            result = await self.neo4j.execute_read_query(query, params)
            
            logger.info(f"Resultado da busca por telefone: {result}")
            
            if result and len(result) > 0:
                user_data = result[0]
                return result[0]
            else:
                None;       
        except Exception as e:
            logger.error(f"Erro ao buscar usuário por telefone: {str(e)}")
            return {
                "found": False,
                "error": str(e)
            }

    async def app_access_fetch(self,email:str,app_idx:str):
        """
        Crie uma query que encontre a conta do usuario no app informado (app_idx) e retorne os seguintes dados:
        -caso a conta seja gerenciada por um negócio) :
           -Nome do negocio como negocio_nome 
           - O idx do negocio como negocio_idx
        -Nivel de acesso do usuario como nivel_id
        -idx da conta como idx
        """

        logger.info(f"===== app_access_fetch() ======")
        query = """
        MATCH (u:Pessoa {email: $email})-[:POSSUI_CONTA]->(ca:Conta)-[:ACESSO_AO_APP]->(a:App {idx: $app_idx})
        OPTIONAL MATCH (ca)-[:GERENCIADO_POR]->(n:Negocio)
        RETURN
          ca.idx AS idx,
          ca.nivelId AS nivel_id,
          n.nome AS negocio_nome,
          n.idx AS negocio_idx
        """
        logger.info(f"query: {query}")
        params = {"email": email, "app_idx": app_idx}
        result = await self.neo4j.execute_read_query(query, params)
        logger.info(f"result: {result}")
        return result


    async def app_user_save(self,data:dict):
        print("===== app_user_save() ======")
        print("data",data)
        result = await self.mysql.add("APP_NEGOCIO_USUARIO",data)
        #result = 0
        print("result",result)
        return {"ID":result}



    async def app_business_user_save(self,data:dict):
        print("===== app_business_user_save() ======")

        #Verifica se é um novo usuario
        #se data não tiver ID ou se ID for 0, então é um novo usuario
        print("data",data)
        if not data.get("ID") or data.get("ID") == 0:
            #se data nao tiver USUARIO_IDX , NEGOCIO_IDX , ou APP_IDX retornar 0
            if not data.get("USUARIO_IDX")  or not data.get("APP_IDX"):
                return {"ERRO":"Usuario, Negocio e App são obrigatórios"}
  
        print("data",data)
        result = await self.mysql.add("APP_NEGOCIO_USUARIO",data)
        #result = 0
        print("result",result)
        return {"ID":result}
    
    async def get_users(self,business_id:int):
        result = await self.mysql.fetch("*","APP_NEGOCIO_USUARIO",[f"NEGOCIO_ID={business_id}","EXCLUIDO=0"])
        return result

    async def get_users_by_business_idx(self,business_idx:int):
        print("===== agent_user.get_users_by_business_idx() =====",business_idx)
        query = f"""SELECT * FROM APP_NEGOCIO_USUARIO WHERE NEGOCIO_IDX='{business_idx}' AND EXCLUIDO=0;"""
        print("query",query)
        result = await self.mysql.query(query)
        print("result",result)
        return result
    
    async def get_roles(self,area_id:int):
        #print("===== agent_user.get_roles() =====",area_id)
        #crie uma query que carregue  da tabela USUARIO_NIVEL a coluna NOME onde o AREA_ID seja igual ao parametro area_id ou AREA_ID é zero
        query = f"""SELECT ID,NOME FROM USUARIO_NIVEL WHERE AREA_ID = {area_id} OR AREA_ID = 0;"""
        result = await self.mysql.query(query)
        return result
    
    async def update_users(self,data:dict):
        #print("===== agent_user.update_users() =====",data)
        updates = []
        for record in data["atualizados"]:
            set_clause = ", ".join([f"{key} = '{value}'" for key, value in record.items() if key != "IDX"])
            query = f"UPDATE APP_NEGOCIO_USUARIO SET {set_clause} WHERE IDX = '{record['IDX']}';"
            updates.append(query)
        # Combine all the update queries into a single statement
        query_update = " ".join(updates)
        result = await self.mysql.query(query_update)

        return result


    async def create_users(self, data: dict):
        print("##### create_users() #####",data)
        inserts = []
        for record in data["adicionados"]:
            columns = ", ".join(record.keys())
            values = ", ".join([f"'{value}'" for value in record.values()])
            query = f"INSERT INTO APP_NEGOCIO_USUARIO ({columns}) VALUES ({values});"
            inserts.append(query)
        # Combine all the insert queries into a single statement
        query_insert = " ".join(inserts)
        print("query_insert",query_insert)
        result = await self.mysql.query(query_insert)
        print("result",result)
    
        return result

    async def admin_emails_fetch(self, negocio_idx: str):
        query = f"""
        SELECT DISTINCT vanu.USUARIO_NOME AS NOME, vanu.USUARIO_EMAIL AS EMAIL
        FROM VISAO_APP_NEGOCIO_USUARIO vanu
        WHERE vanu.NEGOCIO_IDX = '{negocio_idx}'
        AND vanu.NIVEL_ID = 1
        AND vanu.EXCLUIDO = 0
        """
        try:
            result = await self.mysql.query(query)
            return result
        except Exception as e:
            print(f"Erro ao executar a consulta: {e}")
            return []

    async def listar_usuarios_monitor(self):
        """
        Lista todos os usuários disponíveis para o filtro do monitor de mensagens
        Retorna usuários únicos da tabela USUARIO que não estão excluídos
        """
        try:
            query = "SELECT IDX, NOME FROM USUARIO WHERE EXCLUIDO = 0 ORDER BY NOME LIMIT 100"
            result = await self.mysql.query(query)
            return result
        except Exception as e:
            print(f"Erro ao buscar usuários para monitor: {e}")
            return []

    @staticmethod
    def generate_password_token(usuario_idx: str):
        token =  usuario_idx + "-" + generate_unique_id()
        
        return token

    async def password_recovery(self, email: str, app_nome: str):
        print("***** /password/recovery *****")
        # Verifica se o usuário existe
        usuario = await self.fetch("IDX,NOME,EMAIL", [f"EMAIL='{email}'"])
        if "ERRO" in usuario:
            return {"ERRO": "Usuario não encontrado"}
        
        # Gera token de recuperação
        token = User.generate_password_token(usuario["IDX"])
        print("token",token)
        
        # insere o token no banco de dados
        data = {
            "USUARIO_IDX": usuario["IDX"],
            "TOKEN": token,
            "DATA_CRIACAO": datetime.now(),
            "DATA_EXPIRACAO": datetime.now() + timedelta(days=1),
            "USADO": 0
        }
        await self.mysql.add("USUARIO_TOKEN",data)

        # Prepara o email de recuperação
        link_recuperacao = f"https://www.gptalk.com.br/app_phj/app/{app_nome}?email_token={token}"
        print("link_recuperacao",link_recuperacao)
        email_data = {
            "app": "GPTALK",
            "conta": "suporte",
            "assunto": "Recuperação de Senha",
            "destinatario": email,
            "html": f"""
            <h2>Olá {usuario['NOME']},</h2>
            <p>Recebemos uma solicitação para recuperar sua senha.</p>
            <p>Para criar uma nova senha, clique no link abaixo:</p>
            <p><a href="{link_recuperacao}">{link_recuperacao}</a></p>
            <p>Se você não solicitou a recuperação de senha, ignore este email.</p>
            <p>Este link é válido por 24 horas.</p>
            <br>
            <p>Atenciosamente,</p>
            <p>Equipe GPTalk</p>
            """
        }
        
        # Envia o email
        email_sender = Email()
        #print("email_sender",email_sender)
        result = await email_sender.send(email_data)
        #print("result do envio de email",result)
        if any(r["status"] == "ERRO" for r in result):
            return {"ERRO": "Falha ao enviar email de recuperação"}
        
        return {"SUCESSO": "Email de recuperação enviado com sucesso"}

    async def redefinition(self, nova_senha: str, email_token: str):
        if not nova_senha or not email_token:
            return {"ERRO": "Senha e token são obrigatórios"}
        
        # Busca o token na tabela USUARIO_TOKEN
        query = f"""
            SELECT USUARIO_IDX, DATA_EXPIRACAO 
            FROM USUARIO_TOKEN 
            WHERE TOKEN = '{email_token}'
            AND USADO = 0
        """
        
        result = await self.mysql.query(query)
        
        if not result:
            return {"ERRO": "Token não encontrado"}
        
        token_info = result[0]
        data_expiracao_str = token_info["DATA_EXPIRACAO"]
        
        try:
            # Supondo que o formato seja 'YYYY,MM,DD'
            ano, mes, dia = map(int, data_expiracao_str.split(','))
            data_expiracao = datetime(ano, mes, dia)
        except ValueError:
            print(f"Erro ao converter data: {data_expiracao_str}")
            return {"ERRO": "Erro ao processar data de expiração"}
        
        if datetime.now() > data_expiracao:
            return {"ERRO": "Token vencido. Gere novamente uma nova solicitação"}
        
        usuario_idx = token_info["USUARIO_IDX"]
        usuario = await self.fetch("ID,IDX", [f"IDX='{usuario_idx}'"])
        print("usuario",usuario)
        if "ERRO" in usuario:
            return {"ERRO": "Usuário não encontrado"}


        query_update = f"""
            UPDATE USUARIO
            SET SENHA = '{nova_senha}'
            WHERE IDX = '{usuario_idx}'
        """
        await self.mysql.query(query_update)
    
        
        #query_update = f"""
        #    UPDATE USUARIO_TOKEN 
        #    SET USADO = 1 
        #    WHERE TOKEN = '{email_token}'
        #"""
        #await self.mysql.query(query_update)
        
        return {"SUCESSO": "Senha atualizada com sucesso"}

    async def llm_idx_save(self, llm_idx: str, conta_idx: str):
            """
            Remove qualquer relacionamento USANDO anterior e cria um novo entre Conta e  Llm.
            """
            # Garante que llm_idx seja sempre string (idx é string no banco)
            llm_idx_param = str(llm_idx)
    
            # Remove relacionamentos USANDO antigos
            query_delete = """
            MATCH (ca:Conta {idx: $conta_idx})-[r:USANDO]->(:Llm)
            DELETE r
            """
            await self.neo4j.execute_write_query(query_delete, {"conta_idx": conta_idx})
    
            # Cria o novo relacionamento USANDO
            query_merge = """
            MATCH (ca:Conta {idx: $conta_idx})
            MATCH (llm:Llm {idx: $llm_idx})
            MERGE (ca)-[:USANDO]->(llm)
            RETURN ca.idx AS idx, llm.idx AS llm_idx
            """
            result_merge = await self.neo4j.execute_write_query(query_merge, {"conta_idx":  conta_idx, "llm_idx": llm_idx_param})
            logger.info(f"result_merge: {result_merge}")
    
@router.post("/account/llm/set")
async def account_llm_set(data: dict):
    print("##### /agente/user/llm/idx/save #####",data)
    user = User()
    llm_idx:str = data.get("llm_modelo")
    conta_idx:str = data.get("conta_idx")
    result = await user.llm_idx_save(llm_idx, conta_idx)
    return result



@router.post("/update/users")
async def update_users(data: dict):
    #print("##### /aente/user/update/users #####",data)
    user = User()
    result = await user.update_users(data)
    return result

@router.post("/create")
async def create(data: dict):
    print("##### /aente/user/create #####",data)
    print("data",data)
    user = User()
    response = await user.create_by_email_password(**data)
    print("response",response)
    return response

@router.post("/fetch/email_password")
async def fetch_email_password(data: dict):
    print("##### /aente/user/fetch/email_password #####",data)  
    user = User()
    email = data.get("email")
    password = data.get("senha")

    # Linha 881-882
    result = await user.neo4j.execute_read_query(
        "MATCH (n:Pessoa {email: $email}) RETURN n.senha AS senha, n.idx AS idx, n.nome AS nome, n.email AS email, n.telefone AS telefone",
        {"email": email}
    )
    print("result",result)
    if not result:
        return {"ERRO":"Usuario não encontrado"}
    else:
        if result[0]['senha'] != password:
            return {"ERRO":"Senha incorreta"}
        else:
            return result

@router.post("/password/reset")
async def reset_password(data: dict):
    user = User()
    token = data.get("token")
    new_password = data.get("new_password")
    result = await user.password_reset(token,new_password)
    return result


@router.post("/password/token")
async def password_token(data: dict):
    user = User()
    email = data.get("email")
    telephone = data.get("telefone")
    token = User.generate_password_token(email=email,telephone=telephone)
    usuario  = None
    if email:
         usuario = await user.fetch("ID",["EMAIL='{}'".format(email)])
         if 'ERRO' in usuario:
            return {"ERRO":"Usuario não encontrado"}  
         else:
            await user.update({"SENHA_TOKEN":token,"EMAIL":email,"ID": usuario["ID"]})  

    if telephone:
         usuario = await user.fetch("ID",["EMAIL='{}'".format(email)])
         if 'ERRO' in usuario:
            return {"ERRO":"Usuario não encontrado"}  
         else:
            await user.update({"SENHA_TOKEN":token,"TELEFONE":telephone,"ID": usuario["ID"]})  



    
        
    return {"TOKEN":token}

@router.post("/app/access/fetch")
async def app_access_fetch(data: dict):
    print("##### /agente/user/app/access/fetch #####",data)
    user = User()
    user_email = data.get("user_email")
    app_idx = data.get("app_idx")
    result = await user.app_access_fetch(user_email,app_idx)

    return result



@router.post("/app_user/save")
async def app_user_save(data: dict):
    print("##### /agente/user/app_user/save #####",data)
    user = User()
    result = await user.app_user_save(data)
    #print("result",result)
    return {"success":True}


@router.post("/app_business_user/save")
async def app_business_user_save(data: dict):
    #print("##### /agente/user/app_business_user/save #####",data)
    user = User()
    result = await user.app_business_user_save(data)
    #print("result",result)
    return {"success":True}

@router.post("/create/users")
async def create_users(data: dict):
    #print("##### /aente/user/create/users #####",data)
    user = User()
    
    result = await user.create_users(data)
    return result




@router.get("/get/users")
async def get_users(business_id:int):
    user = User()
    result = await user.get_users(business_id)
    #print("result xxxxxxxxx",result)
    return result

@router.get("/get/users/business_idx")
async def get_users_by_business_idx(business_idx:str):
    user = User()
    result = await user.get_users_by_business_idx(business_idx)
    print("result xxxxxxxxx" , result)
    return result




@router.get("/get/roles")
async def get_roles(area_id:int):
    user = User()
    result = await user.get_roles(area_id)
    #print("result xxxxxxxxx",result)
    return result


@router.post("/password/check")
async def password_check(data: dict):
    user = User()
    usuario_idx = data.get("IDX")
    senha = data.get("SENHA")
    
    if not usuario_idx or not senha:
        return {"status":"erro", "mensagem": "Usuário e senha são obrigatórios"}
    
    result = await user.fetch("SENHA", [f"IDX='{usuario_idx}'"])
    
    if "ERRO" in result:
        return {"status":"erro", "mensagem": "Usuário não encontrado"}
    
    if result["SENHA"] == senha:
        return {"status": "sucesso", "mensagem": "Senha correta"}
    else:
        return {"status": "erro", "mensagem": "Senha incorreta"}


@router.get("/admin/emails/fetch/{negocio_idx}")
async def admin_emails_fetch(negocio_idx: str):
    user = User()
    result = await user.admin_emails_fetch(negocio_idx)
    return result

@router.post("/password/recovery")
async def password_recovery(data: dict):
    user = User()
    email = data.get("email")
    app_nome = data.get("app_nome")
    
    if not email:
        return {"ERRO": "Email é obrigatório"}
    
    if not app_nome:
        return {"ERRO": "Nome do app é obrigatório"}
    
    result = await user.password_recovery(email, app_nome)
    return result

@router.post("/password/redefinition")
async def password_redefinition(data: dict):
    user = User()
    nova_senha = data.get("novaSenha")
    email_token = data.get("email_token")
    
    if not nova_senha or not email_token:
        return {"ERRO": "Senha e token são obrigatórios"}
    
    # Busca o token na tabela USUARIO_TOKEN
    query = f"""
        SELECT USUARIO_IDX, DATA_EXPIRACAO 
        FROM USUARIO_TOKEN 
        WHERE TOKEN = '{email_token}'
        AND USADO = 0
    """
    
    result = await user.mysql.query(query)
    
    if not result:
        return {"ERRO": "Token não encontrado"}
    
    token_info = result[0]
    data_expiracao_str = token_info["DATA_EXPIRACAO"]
    
    try:
        # Supondo que o formato seja 'YYYY,MM,DD'
        ano, mes, dia = map(int, data_expiracao_str.split(','))
        data_expiracao = datetime(ano, mes, dia)
    except ValueError:
        print(f"Erro ao converter data: {data_expiracao_str}")
        return {"ERRO": "Erro ao processar data de expiração"}
    
    # Verifica se o token expirou
    if datetime.now() > data_expiracao:
        return {"ERRO": "Token vencido. Gere novamente uma nova solicitação"}
    
    # Extrai o IDX do usuário do token
    usuario_idx = token_info["USUARIO_IDX"]
    
    # Busca o usuário pelo IDX
    usuario = await user.fetch("ID", [f"IDX='{usuario_idx}'"])
    
    if "ERRO" in usuario:
        return {"ERRO": "Usuário não encontrado"}
    
    # Atualiza a senha do usuário
    await user.update({
        "ID": usuario["ID"],
        "SENHA": nova_senha
    })
    
    # Marca o token como usado
    query_update = f"""
        UPDATE USUARIO_TOKEN 
        SET USADO = 1 
        WHERE TOKEN = '{email_token}'
    """
    await user.mysql.query(query_update)
    
    return {"SUCESSO": "Senha atualizada com sucesso"}


@router.get("/search/phone")
async def search_phone_endpoint(phone: str):
    """
    Endpoint para buscar usuário por número de telefone
    Parâmetro: phone (string) - número de telefone do usuário
    Retorna: idx, nome e email se encontrado
    """
    try:
        logger.info(f"===== /agent/user/search/phone =====")
        logger.info(f"Telefone recebido: {phone}")
        
        if not phone:
            return {"ERRO": "Número de telefone é obrigatório"}
        
        # Remove caracteres especiais do telefone
        phone_clean = phone.replace("-", "").replace("(", "").replace(")", "").replace(" ", "")
        
        user = User()
        result = await user.search_phone(phone_clean)
        
        logger.info(f"Resultado da busca: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Erro no endpoint search/phone: {str(e)}")
        return {"ERRO": str(e)}


if __name__ == "__main__":
    import asyncio
    from fastapi.testclient import TestClient
    from api.main import app

    async def test_admin_emails_fetch():
        client = TestClient(app)
        negocio_idx = "1038754321"  # Substitua pelo IDX de um negócio real para teste
        
        response = client.get(f"/api/agent/user/admin/emails/fetch/{negocio_idx}")

        # Verificações adicionais
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        assert isinstance(data, list), "A resposta deve ser uma lista"
        if data:
            assert "NOME" in data[0], "Cada item deve conter a chave 'NOME'"
            assert "EMAIL" in data[0], "Cada item deve conter a chave 'EMAIL'"
        else:
            print("Aviso: A lista de emails está vazia. Verifique se há administradores para este negócio.")

    async def test_fetch_email_password():
        print("test_fetch_email_password()")
        client = TestClient(app)
        
        # Teste com credenciais válidas
        dados_teste = {
            "email": "<EMAIL>",
            "senha": "1"
        }
        
        response = client.post("/api/agent/user/fetch/email_password", json=dados_teste)
        print("response",response)
        # Verificações
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        print("data",data)
        
        # Verifica se a resposta contém erro (caso usuário não exista)
        if "ERRO" in data:
            assert data["ERRO"] in ["Usuario não encontrado", "Senha incorreta"], "Mensagem de erro inesperada"
        else:
            # Verifica se os campos esperados estão presentes
            campos_esperados = ["idx", "nome", "email", "telefone"]
            for campo in campos_esperados:
                assert campo in data[0], f"Campo {campo} não encontrado na resposta"

    async def test_password_recovery():
        print("test_password_recovery()")
        client = TestClient(app)
        
        # Teste com email e app_nome válidos
        dados_teste = {
            "email": "<EMAIL>",
            "app_nome": "restauranteinteligente"
        }
        
        response = client.post("/api/agent/user/password/recovery", json=dados_teste)
        print("response", response)
        
        # Verificações
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        print("data", data)
        
        # Verifica se a resposta contém erro ou sucesso
        assert "ERRO" in data or "SUCESSO" in data, "Resposta deve conter ERRO ou SUCESSO"
        
        # Teste com email inválido
        dados_teste_invalido = {
            "email": "<EMAIL>",
            "app_nome": "restauranteinteligente"
        }
        
        response_invalido = client.post("/api/agent/user/password/recovery", json=dados_teste_invalido)
        data_invalido = response_invalido.json()
        
        # Verifica se retorna erro para email inexistente
        assert "ERRO" in data_invalido, "Deve retornar erro para email inexistente"
        assert data_invalido["ERRO"] == "Usuario não encontrado", "Mensagem de erro incorreta"
        
        # Teste sem email
        dados_teste_sem_email = {
            "app_nome": "restauranteinteligente"
        }
        
        response_sem_email = client.post("/api/agent/user/password/recovery", json=dados_teste_sem_email)
        data_sem_email = response_sem_email.json()
        
        # Verifica se retorna erro quando email não é fornecido
        assert "ERRO" in data_sem_email, "Deve retornar erro quando email não é fornecido"
        assert data_sem_email["ERRO"] == "Email é obrigatório", "Mensagem de erro incorreta"

        # Teste sem app_nome
        dados_teste_sem_app = {
            "email": "<EMAIL>"
        }
        
        response_sem_app = client.post("/api/agent/user/password/recovery", json=dados_teste_sem_app)
        data_sem_app = response_sem_app.json()
        
        # Verifica se retorna erro quando app_nome não é fornecido
        assert "ERRO" in data_sem_app, "Deve retornar erro quando app_nome não é fornecido"
        assert data_sem_app["ERRO"] == "Nome do app é obrigatório", "Mensagem de erro incorreta"

    async def criar_token_teste(user: User, tipo: str = "valido"):
        usuario_idx = "0068108573"  # Use um IDX válido do seu banco
        token = f"token_teste_{tipo}"
        data_atual = datetime.now()
        
        if tipo == "valido":
            data_expiracao = data_atual + timedelta(days=1)
        else:  # expirado
            data_expiracao = data_atual - timedelta(days=1)
        
        # Formata as datas como strings no formato 'YYYY,MM,DD'
        data_atual_str = data_atual.strftime('%Y,%m,%d')
        data_expiracao_str = data_expiracao.strftime('%Y,%m,%d')
        
        dados_token = {
            "USUARIO_IDX": usuario_idx,
            "TOKEN": token,
            "DATA_CRIACAO": data_atual_str,
            "DATA_EXPIRACAO": data_expiracao_str,
            "USADO": 0
        }
        
        await user.mysql.add("USUARIO_TOKEN", dados_token)
        return token

    async def test_password_redefinition():
        print("test_password_redefinition()")
        
        # Criar usuário e tokens de teste
  
        
        # Teste com token válido
        dados_teste_valido = {
            "novaSenha": "123",
            "email_token": "1234567890-4365847204"
        }

        user = User()
        client = TestClient(app)
        
        response = client.post("/api/agent/user/password/redefinition", json=dados_teste_valido)
        print("response", response)
        data = response.json()
        print("data", data)


      

    async def test_create():
        print("test_create()")
        client = TestClient(app)
        
        # Teste com dados válidos
        dados_teste = {
            "email": "<EMAIL>",
            "senha": "senha123",
            "telefone": "11999999993",
            "nome": "Novo Usuário",
            "idx": generate_unique_id()
        }
        
        response = client.post("/api/agent/user/create", json=dados_teste)
        print("response", response)
        
        
        # Verificações
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        print("data", data)
        
        # Verifica se a resposta contém os campos esperados
        assert "ID" in data, "Resposta deve conter o ID do usuário criado"
        assert "EMAIL" in data, "Resposta deve conter o email do usuário"
        assert "NOME" in data, "Resposta deve conter o nome do usuário"
        assert data["EMAIL"] == dados_teste["email"], "Email deve corresponder ao enviado"
        assert data["NOME"] == dados_teste["nome"], "Nome deve corresponder ao enviado"
        
        # Teste com email já cadastrado
        response_email_duplicado = client.post("/api/agent/user/create", json=dados_teste)
        data_email_duplicado = response_email_duplicado.json()
        assert "ERRO" in data_email_duplicado, "Deve retornar erro para email duplicado"
        assert data_email_duplicado["ERRO"] == "Email já cadastrado", "Mensagem de erro incorreta para email duplicado"
        
        # Teste com telefone já cadastrado
        dados_teste_telefone = dados_teste.copy()
        dados_teste_telefone["email"] = "<EMAIL>"
        response_telefone_duplicado = client.post("/api/agent/user/create", json=dados_teste_telefone)
        data_telefone_duplicado = response_telefone_duplicado.json()
        assert "ERRO" in data_telefone_duplicado, "Deve retornar erro para telefone duplicado"
        assert data_telefone_duplicado["ERRO"] == "Telefone já cadastrado", "Mensagem de erro incorreta para telefone duplicado"


    async def test_access_fetch():
        print("test_access_fetch()")
        client = TestClient(app)
        
        # Teste com credenciais válidas
        dados_teste = {
            "user_email": "<EMAIL>",
            "app_idx": "5544332211"
        }
        
        response = client.post("/api/agent/user/app/access/fetch", json=dados_teste)
        print("response", response)
    
    async def test_get_users_by_business_idx():
        print("test_get_users_by_business_idx()")
        client = TestClient(app)
        
        # Teste com business_idx válido
        business_idx = "0068108573"  # Substitua por um business_idx válido

        
        response = client.get(f"/api/agent/user/get/users/business_idx?business_idx={business_idx}")
        print("response", response)

        
        # Verificações
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        print("data", data)
        
        # Verifica se a resposta é uma lista
        assert isinstance(data, list), "A resposta deve ser uma lista"
        
        # Se houver dados, verifica a estrutura de cada usuário
        if data:
            for usuario in data:
                # Verifica campos esperados em cada registro de usuário
                campos_esperados = ["ID", "NEGOCIO_IDX", "EXCLUIDO"]
                for campo in campos_esperados:
                    assert campo in usuario, f"Campo {campo} não encontrado no registro do usuário"
                
                # Verifica se NEGOCIO_IDX corresponde ao solicitado
                assert usuario["NEGOCIO_IDX"] == business_idx, "NEGOCIO_IDX deve corresponder ao solicitado"
                assert usuario["EXCLUIDO"] == 0, "Apenas usuários não excluídos devem ser retornados"

    async def test_create_users():
        print("test_create_users()")
        client = TestClient(app)
        
        # Dados de teste
        dados_teste = {
            "adicionados": [
                {
                    "IDX": "2755545430",
                    "ID_PAI": "0",
                    "USUARIO_APELIDO": "Glayson",
                    "USUARIO_EMAIL": "<EMAIL>",
                    "NIVEL_ID": "2",
                    "APP_IDX": "4567890123",
                    "NEGOCIO_IDX": "0068108573",
                    "DATA_CAD": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            ]
        }
        
        response = client.post("/api/agent/user/create/users", json=dados_teste)
        print("response", response)
        
        # Verificações
        assert response.status_code == 200, f"Erro no status code: {response.status_code}"
        data = response.json()
        print("data", data)
        
        # Verifica se o usuário foi criado consultando o banco
        user = User()
        for usuario in dados_teste["usuarios"]:
            result = await user.fetch("ID,USUARIO_EMAIL,USUARIO_APELIDO", [f"USUARIO_EMAIL='{usuario['USUARIO_EMAIL']}'"])
            assert "ERRO" not in result, f"Usuário {usuario['USUARIO_EMAIL']} não foi criado"
            assert result["USUARIO_APELIDO"] == usuario["USUARIO_APELIDO"], "Apelido do usuário não corresponde"


    async def test_app_business_user_save():
        
        # Dados de teste
        dados_teste = {
            "USUARIO_IDX": "2755545430",
            "NEGOCIO_IDX": "0068108573",
            "APP_IDX": "0068108573",
            "PLANO_ID": 1,
            "NIVEL_ID": 2,
            "PLANO_INICIO": None,
            "PLANO_VALIDADE": None
        }
        response = await app_business_user_save(dados_teste)
        #print("final response", response)
        





    # Execução dos testes
    #asyncio.run(test_admin_emails_fetch())
    #asyncio.run(test_fetch_email_password())
    #asyncio.run(test_password_recovery())
    #asyncio.run(test_password_redefinition())
    #asyncio.run(test_create())
    #asyncio.run(test_get_users_by_business_idx())
    #asyncio.run(test_create_users())
    asyncio.run(test_access_fetch())
        
    #asyncio.run(test_app_business_user_save())